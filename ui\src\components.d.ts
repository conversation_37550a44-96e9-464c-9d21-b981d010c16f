/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/tree/main/packages/vue-components
export {}

declare module "vue" {
  export interface GlobalComponents {
    BulletinDetail: typeof import("./layouts/employee/BulletinDetail.vue")["default"];
    CustomerSearch: typeof import("./components/CustomerSearch.vue")["default"];
    HelloWorld: typeof import("./components/HelloWorld.vue")["default"];
    HelpContent: typeof import("./layouts/employee/HelpContent.vue")["default"];
    HelpCustomize: typeof import("./layouts/employee/HelpCustomize.vue")["default"];
    MessageDetailModal: typeof import("./components/MessageDetailModal.vue")["default"];
    Messages: typeof import("./components/Messages.vue")["default"];
    NewBulletin: typeof import("./layouts/employee/NewBulletin.vue")["default"];
    NotificationModal: typeof import("./components/NotificationModal.vue")["default"];
    PermissionDeniedModal: typeof import("./components/PermissionDeniedModal.vue")["default"];
    TheWelcome: typeof import("./components/TheWelcome.vue")["default"];
    WelcomeItem: typeof import("./components/WelcomeItem.vue")["default"];
  }
}
