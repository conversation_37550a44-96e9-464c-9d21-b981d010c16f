<template>
  <header class="text-sm leading-none">
    <div
      class="flex items-center justify-between px-6 py-3 w-full bg-slate-800 shadow-[0px_2px_12px_rgba(0,0,0,0.05)]"
    >
      <div class="flex items-center gap-8">
        <h1 class="text-lg font-bold text-white">
          Employee Dashboard
        </h1>
        <!-- Dashboard Actions -->
        <nav class="flex items-center gap-4 font-medium text-cyan-400">
          <!-- Customize -->
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <button class="hover:text-cyan-300 transition-colors">
              Customize
            </button>
          </div>

          <div class="w-px h-4 bg-gray-400"></div>

          <!-- Help -->
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
            </svg>
            <button class="hover:text-cyan-300 transition-colors">
              Help
            </button>
          </div>

          <div class="w-px h-4 bg-gray-400"></div>

          <!-- Notices -->
          <div class="flex items-center gap-2">
            <svg class="w-5 h-5 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/>
            </svg>
            <button class="hover:text-cyan-300 transition-colors">
              Notices (2)
            </button>
          </div>
        </nav>
      </div>
      <!-- Store Selector -->
      <div class="flex gap-2.5 my-auto text-white">
        <label class="grow">Selected Store:</label>
        <button
          class="font-medium basis-auto hover:text-gray-200 transition-colors flex items-center gap-2.5"
          @click="toggleDropdown"
          :aria-expanded="isOpen"
          aria-haspopup="true"
        >
          <span>{{ selectedStore }}</span>
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/b12b1a958173b88ed4f27b9cf7b6a3307c4dbb6c?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 my-auto w-3.5 aspect-[1.56]"
            alt="Dropdown arrow"
          />
        </button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  selectedStore?: string
}

withDefaults(defineProps<Props>(), {
  selectedStore: 'Unused Store 2X'
})

const isOpen = ref(false)

const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}
</script>
