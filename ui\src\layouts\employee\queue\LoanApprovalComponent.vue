<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Title -->
    <div class="text-sky-950 text-base font-bold font-roboto leading-tight">
      Loans Eligible for Approval
    </div>

    <!-- Show content only when not collapsed -->
    <template v-if="!isCollapsed">
      <!-- Header metrics -->
      <div class="text-sky-950 text-3xl font-bold font-roboto leading-tight mt-2">{{ loanApprovalData.totalLoans }}</div>
      <div class="text-gray-500 text-sm font-roboto mt-4">Sorted by ACH provider</div>

            <!-- Divider -->
      <div class="w-full h-px bg-slate-500 mt-4"></div>

      <!-- Details view controls row -->
      <div v-if="showDetails" class="flex items-center justify-between mb-3">
        <button
          @click="goBack"
          class="flex items-center gap-2 text-cyan-600 text-sm font-semibold font-roboto leading-tight hover:bg-cyan-50 px-2 py-1 rounded transition-colors"
          aria-label="Back"
        >
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M11.53 6.47a.75.75 0 0 1 0 1.06l-3.72 3.72H18a.75.75 0 0 1 0 1.5H7.81l3.72 3.72a.75.75 0 1 1-1.06 1.06l-5-5a.75.75 0 0 1 0-1.06l5-5a.75.75 0 0 1 1.06 0"/></svg>
        </button>
        <div class="bg-cyan-600 text-white px-3 py-1 rounded-full text-xs font-semibold font-roboto">
          {{ selectedProvider?.loanCount }} ({{ selectedProvider?.percentage }}%)
        </div>
      </div>

      <!-- Main list view -->
      <template v-if="!showDetails">
        <template v-if="!loanApprovalData.hasLoans">
          <div class="flex flex-col items-center justify-center flex-1 text-center">
            <div class="text-sky-950 text-lg font-semibold font-roboto mb-2">No loans eligible for approval</div>
            <div class="text-gray-500 text-sm font-roboto">There are no loans eligible for approval sorted by ACH providers today.</div>
          </div>
        </template>
        <template v-else>
          <div class="mt-4 space-y-4">
            <div v-for="(provider, index) in sortedProviders" :key="provider.id" class="relative">
              <div class="flex justify-between items-center">
                <div class="flex-1 min-w-0">
                  <div class="text-sky-950 text-sm font-semibold font-roboto hover:text-cyan-600 transition-colors text-left">{{ index + 1 }}. {{ provider.name }}</div>
                  <button
                    @click="viewDetails(provider, index)"
                    class="text-cyan-600 text-xs font-semibold font-roboto hover:underline mt-1"
                  >
                    View details »
                  </button>
                </div>
                <div class="shrink-0">
                  <button
                    @click="applyQuickFilter(provider)"
                    class="bg-cyan-600 text-white px-3 py-1 rounded-full text-xs font-semibold font-roboto hover:bg-cyan-700 transition-colors"
                    title="Apply quick filter to table"
                  >
                    {{ provider.loanCount }} ({{ provider.percentage }}%)
                  </button>
                </div>
              </div>
              <div v-if="index < sortedProviders.length - 1" class="w-full h-px bg-slate-300 mt-3"></div>
            </div>
          </div>
        </template>
      </template>

      <!-- Provider details view content -->
      <template v-else>
        <div class="mt-1">
          <div class="text-sky-950 text-base font-bold font-roboto leading-tight">{{ selectedIndex + 1 }}. {{ selectedProvider?.name }}</div>
          <div class="text-gray-500 text-sm font-roboto mt-4 mb-1">Loan(s):</div>
          <div class="text-sky-950 text-sm font-roboto leading-relaxed whitespace-pre-line break-words">
            {{ selectedProvider?.loanIds.join(' ') }}
          </div>
        </div>
      </template>

      <!-- Bottom spacing -->
      <div class="flex-1"></div>
      <div class="w-full h-px bg-slate-500 mt-4"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useWidgetsStore } from '@/stores/widgets'
import { useEmployeeStore } from '@/stores/employee'
import type { ACHProvider } from '@/stores/employee'

// Use stores
const widgetsStore = useWidgetsStore()
const employeeStore = useEmployeeStore()
const isCollapsed = computed(() => widgetsStore.isCollapsed)

// Get loan approval data
const loanApprovalData = computed(() => employeeStore.currentLoanApprovalData)

// Sort providers by name
const sortedProviders = computed(() => {
  return [...loanApprovalData.value.providers].sort((a, b) => a.name.localeCompare(b.name))
})

// Details view state
const showDetails = ref(false)
const selectedProvider = ref<ACHProvider | null>(null)

// Methods
const selectedIndex = ref<number>(0)
const viewDetails = (provider: ACHProvider, index: number) => {
  selectedProvider.value = provider
  selectedIndex.value = index
  showDetails.value = true
}

const goBack = () => {
  showDetails.value = false
  selectedProvider.value = null
  selectedIndex.value = 0
}

// Quick filter apply for Criteria 7
const applyQuickFilter = (provider: ACHProvider) => {
  employeeStore.setWidgetFilter({ key: 'achProvider', label: 'ACH Provider', value: provider.name })
  // Optionally keep user in list view; do not switch to details
}
</script>
