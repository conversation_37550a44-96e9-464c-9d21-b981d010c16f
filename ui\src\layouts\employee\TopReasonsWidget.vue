<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Title -->
    <div class="text-sky-950 text-base font-bold font-roboto leading-tight">Top Denied Reasons</div>

    <!-- Expanded content -->
    <template v-if="!isCollapsed">
      <!-- Time Period Selector -->
      <div class="mt-4 flex items-center gap-2.5 flex-wrap">
        <button class="text-cyan-600 text-xs font-semibold font-roboto leading-tight hover:bg-cyan-50 px-2 py-1 rounded transition-colors">Today</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">Yesterday</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">Last Week</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">1 Month</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">3 Months</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">1 Year</button>
      </div>

      <!-- Divider -->
      <div class="w-full h-px bg-slate-500 mt-4 mb-4"></div>

      <!-- Content Area -->
      <div class="flex gap-6 flex-1">
        <!-- Most Used Section -->
        <div class="flex-1">
          <div class="bg-sky-950 rounded-full px-3 py-1 inline-block mb-4">
            <span class="text-white text-xs font-semibold font-roboto">Most Used</span>
          </div>

          <div class="space-y-4">
            <div v-for="(item, index) in employeeStore.currentData.topReasons.slice(0, 5)" :key="item.reason" class="flex flex-col">
              <div class="text-sky-950 text-sm font-semibold font-roboto">{{ index + 1 }}. {{ item.reason }}</div>
              <div class="text-sky-950 text-xs font-normal font-roboto leading-tight mt-1">{{ item.times }} times</div>
            </div>
          </div>
        </div>

        <!-- Vertical Divider -->
        <div class="w-px bg-slate-300"></div>

        <!-- Least Used Section -->
        <div class="flex-1">
          <div class="bg-sky-950 rounded-full px-3 py-1 inline-block mb-4">
            <span class="text-white text-xs font-semibold font-roboto">Least Used</span>
          </div>

          <div class="space-y-4">
            <div v-for="(item, index) in leastUsedReasons" :key="item.reason" class="flex flex-col">
              <div class="text-sky-950 text-sm font-semibold font-roboto">{{ index + 1 }}. {{ item.reason }}</div>
              <div class="text-sky-950 text-xs font-normal font-roboto leading-tight mt-1">{{ item.times }} times</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Divider -->
      <div class="w-full h-px bg-slate-500 mt-4"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWidgetsStore } from '@/stores/widgets'
import { useEmployeeStore } from '@/stores/employee'

// Use stores
const widgetsStore = useWidgetsStore()
const employeeStore = useEmployeeStore()
const isCollapsed = computed(() => widgetsStore.isCollapsed)

// Generate least used reasons (reverse order with lower values)
const leastUsedReasons = computed(() => {
  const topReasons = employeeStore.currentData.topReasons
  return topReasons
    .slice()
    .reverse()
    .map((item, index) => ({
      reason: item.reason,
      times: Math.max(1, Math.floor(item.times / 10)) // Much lower values for least used
    }))
    .slice(0, 5)
})
</script>
