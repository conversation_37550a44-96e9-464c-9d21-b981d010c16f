<template>
  <div class="w-full bg-white rounded-2xl">
    <!-- Widget filter pill area (separate from general Filters) -->
    <div v-if="employeeStore.widgetFilter" class="px-3.5 pt-3">
      <span class="inline-flex items-center bg-cyan-50 text-cyan-800 px-2 py-1 rounded text-xs font-medium">
        {{ employeeStore.widgetFilter.label }}: {{ employeeStore.widgetFilter.value }}
        <button class="ml-2 text-cyan-700" @click="employeeStore.clearWidgetFilter()">×</button>
      </span>
    </div>

    <!-- AG Grid -->
    <div class="ag-theme-quartz w-full text-sky-950" style="height: 420px">
      <AgGridVue
        class="w-full h-full"
        :columnDefs="columnDefs"
        :rowData="filteredRowData"
        :defaultColDef="defaultColDef"
        :pagination="true"
        :paginationPageSize="13"
        :rowSelection="'multiple'"
        :suppressRowClickSelection="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import type { ColDef } from 'ag-grid-community'
import { useEmployeeStore } from '@/stores/employee'

const employeeStore = useEmployeeStore()

// Columns to match existing DeniedQueueTable.vue
const columnDefs = ref<ColDef[]>([
  { field: 'dateAdded', headerName: 'Date Added' },
  { field: 'firstName', headerName: 'First Name', cellClass: 'text-sky-600 font-semibold' },
  { field: 'lastName', headerName: 'Last Name', cellClass: 'text-sky-600 font-semibold' },
  { field: 'store', headerName: 'STORE' },
  { field: 'language', headerName: 'Language' },
  { field: 'ssn', headerName: 'SSN' },
  { field: 'loanNumber', headerName: 'Loan #' },
  { field: 'emailAddress', headerName: 'Email Address' },
  { field: 'state', headerName: 'State' },
  { field: 'leadProvider', headerName: 'Lead Provider' },
  { field: 'denialReason', headerName: 'Denial Reason' },
])

const defaultColDef: ColDef = {
  sortable: true,
  resizable: true,
  filter: true,
  minWidth: 120,
}

// Mock row data (copied from DeniedQueueTable.vue); include achProviderName for potential future mapping
const rowData = ref([
  {
    dateAdded: '11/04/2022, 10:16AM', firstName: 'Jennifer', lastName: 'Moore', store: 'ST901-ID71',
    language: 'English', ssn: '**********', loanNumber: '000026543', emailAddress: '<EMAIL>',
    state: 'TX', leadProvider: '818 OP', denialReason: 'Invalid Documents', achProviderName: 'Dharma'
  },
  {
    dateAdded: '11/04/2022, 10:16AM', firstName: 'Jennifer', lastName: 'Moore', store: 'ST901-ID71',
    language: 'English', ssn: '**********', loanNumber: '000026543', emailAddress: '<EMAIL>',
    state: 'FL', leadProvider: '818 OP', denialReason: 'Unreliable', achProviderName: 'Leadspedia'
  },
  {
    dateAdded: '11/04/2022, 10:16AM', firstName: 'Jennifer', lastName: 'Moore', store: 'ST901-ID71',
    language: 'English', ssn: '**********', loanNumber: '000026543', emailAddress: '<EMAIL>',
    state: 'FL', leadProvider: '818 OP', denialReason: 'Invalid Documents', achProviderName: 'Ping Tree'
  },
  {
    dateAdded: '11/04/2022, 10:16AM', firstName: 'Jennifer', lastName: 'Moore', store: 'ST901-ID71',
    language: 'English', ssn: '**********', loanNumber: '000026543', emailAddress: '<EMAIL>',
    state: 'TX', leadProvider: '818 OP', denialReason: 'Unreliable', achProviderName: 'Dharma'
  }
])

// Filter rows by widget filter if present
const filteredRowData = computed(() => {
  const f = employeeStore.widgetFilter
  if (!f || f.key !== 'achProvider') return rowData.value
  return rowData.value.filter(r => (r as any).achProviderName === f.value)
})
</script>

<style scoped>
/***** AG Grid theme tweaks can go here if needed *****/
</style>

