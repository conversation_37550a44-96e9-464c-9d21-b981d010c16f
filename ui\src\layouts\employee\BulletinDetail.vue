<template>
  <div class="w-full bg-gray-50 min-h-screen">
    <!-- Header with <PERSON> Button -->
    <div class="bg-slate-800 py-3 px-6">
      <div class="flex items-center gap-3">
        <button
          @click="navigateBack"
          class="flex items-center gap-2 text-white hover:text-gray-200 transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span class="text-lg font-medium">Bulletin Detail</span>
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="w-full">
      <div v-if="bulletin" class="w-full">
        <!-- Title Section -->
        <div class="flex items-start justify-between w-full px-6 py-8">
          <div class="flex-1">
            <h1 class="text-slate-800 text-3xl font-bold font-roboto mb-4">
              {{ bulletin.title }}
            </h1>
            <p class="text-gray-500 text-sm font-normal font-roboto mb-6">
              {{ bulletin.date }}
            </p>

            <!-- Content -->
            <div class="max-w-none">
              <p class="text-slate-800 text-base font-normal font-roboto leading-relaxed mb-6">
                {{ bulletin.content }}
              </p>

              <div v-if="bulletin.details" class="space-y-2 mb-6">
                <p v-for="detail in bulletin.details" :key="detail" class="text-slate-800 text-base font-normal font-roboto">
                  {{ detail }}
                </p>
              </div>

              <div v-if="bulletin.additionalInfo">
                <p class="text-slate-800 text-base font-normal font-roboto">
                  {{ bulletin.additionalInfo }}
                </p>
              </div>
            </div>
          </div>

          <!-- Tag positioned at top right -->
          <div class="flex items-center gap-2 ml-8 mt-2">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="text-blue-500 text-sm font-medium font-roboto">{{ bulletin.tag }}</span>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="text-center py-12">
        <p class="text-gray-500 text-lg">Loading bulletin...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'

interface Bulletin {
  id: number
  tag: string
  title: string
  description: string
  date: string
  content: string
  details?: string[]
  additionalInfo?: string
}

// Props
const props = defineProps<{
  bulletinId: number | null
}>()

// Emits
const emit = defineEmits(['navigate-back'])

const bulletin = ref<Bulletin | null>(null)

// Navigation method
const navigateBack = () => {
  emit('navigate-back')
}

// Mock bulletin data - in real app this would come from API
const bulletinData: Record<string, Bulletin> = {
  '1': {
    id: 1,
    tag: 'Urgent',
    title: 'Important update released',
    description: 'Version 3.0.2 is out now. It consists of bug fixes and improved...',
    date: 'AUG 10, 2023',
    content: 'Version 3.0.2 is out now. It consists of bug fixes and improved privacy settings.',
    additionalInfo: 'Thank you for using our system!'
  },
  '2': {
    id: 2,
    tag: 'New',
    title: 'New guide available',
    description: 'Our new user guide "Advanced Tips", is now available in our si...',
    date: 'JUL 28, 2023',
    content: 'Our new user guide "Advanced Tips", is now available in our system.',
    details: [
      'Comprehensive coverage of advanced features',
      'Step-by-step tutorials with screenshots',
      'Best practices from experienced users',
      'Troubleshooting common issues'
    ],
    additionalInfo: 'Access the guide from the Help section in your dashboard.'
  },
  '3': {
    id: 3,
    tag: 'Update',
    title: 'Latest update is here',
    description: 'Version 3.0.1 is out now. It consists of design improvements...',
    date: 'JUL 25, 2023',
    content: 'Version 3.0.1 is out now. It consists of design improvements and new features.',
    details: [
      'Refreshed user interface design',
      'New dashboard widgets',
      'Improved mobile responsiveness',
      'Enhanced search functionality'
    ],
    additionalInfo: 'The update will be automatically applied to your account.'
  }
}

// Load bulletin data based on props
const loadBulletin = () => {
  if (props.bulletinId) {
    bulletin.value = bulletinData[props.bulletinId.toString()] || null
  } else {
    bulletin.value = null
  }
}

// Watch for changes in bulletinId prop
watch(() => props.bulletinId, loadBulletin, { immediate: true })

onMounted(() => {
  loadBulletin()
})
</script>
