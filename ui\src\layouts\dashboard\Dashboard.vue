<template>
  <div class="dashboard-layout min-h-screen bg-white">
    <!-- Header -->
    <DashboardHeader
      class="h-16 fixed top-0 left-0 right-0 z-30"
      @toggle-sidebar="handleSidebarToggle"
    />

    <!-- Main Content Area -->
    <div class="flex h-screen">
      <!-- Sidebar -->
      <div class="fixed left-0 top-16 bottom-0 z-20 hidden md:block">
        <DashboardSidebar
          :is-collapsed="sidebarCollapsed"
          @update-collapsed="handleSidebarUpdate"
        />
      </div>

      <!-- Main Content -->
      <div
        :class="[
          'w-full bg-gray-50 transition-all duration-300',
          {
            'md:ml-16': sidebarCollapsed,
            'md:ml-64': !sidebarCollapsed,
            'ml-0': true
          }
        ]"
        style="margin-top: 64px; min-height: calc(100vh - 64px);"
      >
        <DashboardContent v-if="$route.path === '/dashboard'" />
        <DeniedQueueContent v-else-if="$route.path === '/employee-denied-queue'" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import DashboardSidebar from './DashboardSidebar.vue'
import DashboardContent from './DashboardContent.vue'
import DashboardHeader from './DashboardHeader.vue'
import DeniedQueueContent from '../employee/DeniedQueueContent.vue'

// Sidebar collapse state
const sidebarCollapsed = ref(false)

// Handle sidebar toggle from header
const handleSidebarToggle = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// Handle sidebar state updates from sidebar component
const handleSidebarUpdate = (collapsed: boolean) => {
  sidebarCollapsed.value = collapsed
}
</script>
