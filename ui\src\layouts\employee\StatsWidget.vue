<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Title -->
    <div class="text-sky-950 text-base font-bold font-roboto leading-tight">{{ title }}</div>

    <!-- Show content only when not collapsed -->
    <template v-if="!isCollapsed">
      <!-- Value -->
      <div class="text-sky-950 text-3xl font-bold font-roboto leading-tight mt-2">{{ value }}</div>

      <!-- Time Period Selector -->
      <div class="mt-4 flex items-center gap-2.5">
        <button class="text-cyan-600 text-xs font-semibold font-roboto leading-tight hover:bg-cyan-50 px-2 py-1 rounded transition-colors">Today</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">Yesterday</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">Last Week</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">Last Month</button>
      </div>

      <!-- Divider -->
      <div class="w-full h-px bg-slate-500 mt-4"></div>

      <!-- Items List -->
      <div class="mt-4 space-y-4">
        <div v-for="(item, index) in items" :key="item.label" class="relative">
          <div class="flex justify-between items-center">
            <button class="text-sky-950 text-sm font-semibold font-roboto hover:text-cyan-600 transition-colors text-left">{{ index + 1 }}. {{ item.label }}</button>
            <div class="w-7 h-5 bg-cyan-600 rounded-full flex items-center justify-center">
              <span class="text-white text-xs font-semibold font-roboto">{{ item.value }}</span>
            </div>
          </div>
          <div v-if="index < items.length - 1" class="w-full h-px bg-slate-300 mt-4"></div>
        </div>
      </div>

      <!-- Bottom spacing -->
      <div class="flex-1"></div>
      <div class="w-full h-px bg-slate-500 mt-4"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWidgetsStore } from '@/stores/widgets'

interface StatsItem {
  label: string;
  value: number;
}

interface Props {
  title: string;
  value: number;
  items: StatsItem[];
}

defineProps<Props>();

// Use widgets store
const widgetsStore = useWidgetsStore()
const isCollapsed = computed(() => widgetsStore.isCollapsed)
</script>
