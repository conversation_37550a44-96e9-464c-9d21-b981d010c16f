<!--
  OriginatedLoansCard - Isolated Vue Component

  This component displays originated loans data in a donut chart format.
  It's designed to be completely isolated and can be embedded in ASPX files.

  Usage:
  <OriginatedLoansCard
    :data="{ approved: 45, notApproved: 15, totalAmount: 2500000, dateRange: 'Today' }"
    :isCollapsed="false"
  />

  Props:
  - data: { approved: number, notApproved: number, totalAmount: number, dateRange?: string }
  - isCollapsed: boolean (default: false)

  Build for embedding:
  npm run build
-->
<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      props.isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Header -->
    <div class="flex items-start justify-between">
      <div>
        <div class="text-sky-950 text-base font-bold font-roboto leading-tight">Originated Loans</div>
        <div class="text-slate-500 text-xs font-roboto mt-1">{{ props.data?.dateRange || 'Today' }}</div>
      </div>
      <div class="text-sky-950 bg-slate-100 border border-slate-300 rounded-full px-3 py-1 text-xs font-semibold font-roboto">
        {{ formattedAmount }}
      </div>
    </div>

    <!-- Chart Area -->
    <div v-if="!props.isCollapsed" class="flex-1 grid place-items-center">
      <svg width="350" height="350" viewBox="0 0 200 200" role="img" aria-label="Originated Loans Donut">
        <!-- Donut background -->
        <circle cx="100" cy="100" :r="r" fill="none" :stroke="bgStroke" :stroke-width="strokeW" />

        <!-- Approved arc -->
        <g :transform="'rotate(' + startAngle + ' 100 100)'">
          <circle
            cx="100" cy="100" :r="r" fill="none"
            :stroke="colors.approved" :stroke-width="strokeW"
            :stroke-dasharray="approvedDash"
            stroke-linecap="butt"
          />
        </g>
        <!-- Not Approved arc -->
        <g :transform="'rotate(' + (startAngle + approvedDeg) + ' 100 100)'">
          <circle
            cx="100" cy="100" :r="r" fill="none"
            :stroke="colors.notApproved" :stroke-width="strokeW"
            :stroke-dasharray="notApprovedDash"
            stroke-linecap="butt"
          />
        </g>

        <!-- Center mask and total -->
        <circle cx="100" cy="100" :r="innerR" fill="white" />
        <text x="100" y="100" dominant-baseline="middle" text-anchor="middle" class="fill-sky-950" style="font: 700 22px Roboto">{{ total }}</text>

        <!-- Segment labels (counts with %) -->
        <text v-if="approved > 0" :x="approvedLabel.x" :y="approvedLabel.y" text-anchor="middle" class="fill-sky" style="font: 600 10px Roboto">{{ approved }} ({{ approvedPct }}%)</text>
        <text v-if="notApproved > 0" :x="notApprovedLabel.x" :y="notApprovedLabel.y" text-anchor="middle" class="fill-sky" style="font: 600 10px Roboto">{{ notApproved }} ({{ notApprovedPct }}%)</text>
      </svg>

      <!-- Legend -->
      <div class="mt-2 flex items-center gap-6 justify-start w-full">
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :style="{ background: colors.approved }"></span>
          <span class="text-sky-950 text-xs font-roboto">Approved</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :style="{ background: colors.notApproved }"></span>
          <span class="text-sky-950 text-xs font-roboto">Not Approved Yet</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props interface
interface OriginatedLoansData {
  approved: number
  notApproved: number
  totalAmount: number
  dateRange?: string
}

// Props with default mock data
const props = withDefaults(defineProps<{
  data?: OriginatedLoansData
  isCollapsed?: boolean
}>(), {
  data: () => ({
    approved: 45,
    notApproved: 15,
    totalAmount: 2500000,
    dateRange: 'Today'
  }),
  isCollapsed: false
})

// Derived values
const approved = computed(() => props.data?.approved ?? 0)
const notApproved = computed(() => props.data?.notApproved ?? 0)
const total = computed(() => approved.value + notApproved.value)

const approvedPct = computed(() => total.value === 0 ? 0 : Math.round((approved.value / total.value) * 100))
const notApprovedPct = computed(() => total.value === 0 ? 0 : 100 - approvedPct.value)

const formattedAmount = computed(() => {
  const amt = props.data?.totalAmount ?? 0
  return `$${amt.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
})

// Donut geometry and positions
const cx = 100, cy = 100
const r = 64
const strokeW = 50
const innerR = r - strokeW / 2 + 10 // larger mask so the text never touches the inner ring
const bgStroke = '#E6EEF2'
const circumference = 2 * Math.PI * r
const startAngle = -90 // Start at top

const approvedDeg = computed(() => 360 * (approved.value / (total.value || 1)))
const notApprovedDeg = computed(() => 360 - approvedDeg.value)

const approvedLen = computed(() => circumference * (approved.value / (total.value || 1)))
const notApprovedLen = computed(() => circumference * (notApproved.value / (total.value || 1)))
const approvedDash = computed(() => `${approvedLen.value} ${circumference}`)
const notApprovedDash = computed(() => `${notApprovedLen.value} ${circumference}`)

// Label positions at mid-angles of each arc
const rad = (deg: number) => (deg * Math.PI) / 180
const labelRadius = r - strokeW / 2 + 4
const approvedLabel = computed(() => {
  const mid = startAngle + approvedDeg.value / 2
  return { x: cx + labelRadius * Math.cos(rad(mid)), y: cy + labelRadius * Math.sin(rad(mid)) + 3 }
})
const notApprovedLabel = computed(() => {
  const mid = startAngle + approvedDeg.value + notApprovedDeg.value / 2
  return { x: cx + labelRadius * Math.cos(rad(mid)), y: cy + labelRadius * Math.sin(rad(mid)) + 3 }
})

const colors = {
  approved: '#137AB6', // blue per expected
  notApproved: '#0F172A', // dark navy
}

// Export component name for easier identification when embedded
defineOptions({
  name: 'OriginatedLoansCard'
})
</script>

