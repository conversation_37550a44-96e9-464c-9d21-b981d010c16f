<template>
  <aside
    :class="[
      'sidebar-container transition-all duration-300 ease-in-out h-full',
      isCollapsed ? 'w-16' : 'w-64',
      'bg-white shadow-lg border-r border-gray-200',
      isMobile && isVisible ? 'fixed left-0 top-16 bottom-0 z-50' : '',
      isMobile && !isVisible ? 'hidden md:block' : ''
    ]"
    style="height: calc(100vh - 64px);"
  >
    <!-- Mobile Toggle Button -->
    <button
      v-if="isMobile && !isVisible"
      @click="toggleSidebar"
      class="fixed top-20 left-4 z-50 p-2 bg-cyan-600 text-white rounded-md shadow-lg md:hidden"
      aria-label="Toggle menu"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
      </svg>
    </button>

    <!-- Sidebar Content -->
    <div
      :class="[
        'sidebar-content h-full flex flex-col',
        isMobile && !isVisible ? 'hidden' : 'block'
      ]"
    >

      <!-- Search Bar -->
      <div v-if="!isCollapsed" class="p-3">
        <div class="relative">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Menu search..."
            class="w-full px-4 py-2.5 text-sm border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent"
          />
          <svg class="absolute right-3 top-3 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 overflow-y-auto px-2 pb-2">
        <div class="space-y-1">
          <MenuGroup
            v-for="group in filteredMenuGroups"
            :key="group.id"
            :group="group"
            :is-collapsed="isCollapsed"
            :active-item="activeMenuItem"
            @toggle="toggleGroup"
            @item-click="handleItemClick"
          />
        </div>
      </nav>
    </div>

    <!-- Mobile Overlay -->
    <div
      v-if="isMobile && isVisible"
      @click="closeSidebar"
      class="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
    ></div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import MenuGroup from './MenuGroup.vue'

// Types
interface MenuItem {
  id: string
  name: string
  route: string
}

interface MenuGroupType {
  id: string
  title: string
  icon: string
  isExpanded: boolean
  items: MenuItem[]
}

// Props
interface Props {
  isCollapsed?: boolean
}

// Emits
interface Emits {
  (e: 'update-collapsed', collapsed: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  isCollapsed: false
})

const emit = defineEmits<Emits>()

// Reactive state
const isCollapsed = ref(props.isCollapsed)
const isVisible = ref(false)
const isMobile = ref(false)
const searchQuery = ref('')
const activeMenuItem = ref('')

// Route for active item detection
const route = useRoute()

// Menu data structure with groups
const menuGroups = ref<MenuGroupType[]>([
  {
    id: 'main-menu',
    title: 'Main Menu',
    icon: '📋',
    isExpanded: true,
    items: [
      { id: 'build-call-list', name: 'Build Call List', route: '/dashboard/build-call-list' },
      { id: 'customer-query', name: 'Customer Query', route: '/dashboard/customer-query' },
      { id: 'incoming-applications', name: 'Incoming Applications', route: '/dashboard/incoming-applications' },
      { id: 'insight-center', name: 'Insight Center', route: '/dashboard/insight-center' },
      { id: 'instant-messenger', name: 'Instant Messenger', route: '/dashboard/instant-messenger' },
      { id: 'loans', name: 'Loans', route: '/dashboard/loans' },
      { id: 'new-application', name: 'New Application', route: '/dashboard/new-application' },
      { id: 'pending-applications', name: 'Pending Applications', route: '/dashboard/pending-applications' },
      { id: 'portfolio-analysis', name: 'Portfolio Analysis', route: '/dashboard/portfolio-analysis' },
      { id: 'queues', name: 'Queues', route: '/dashboard/queues' },
      { id: 'report-builder', name: 'Report Builder', route: '/dashboard/report-builder' },
      { id: 'report-results', name: 'Report Results', route: '/dashboard/report-results' },
      { id: 'reports', name: 'Reports', route: '/dashboard/reports' }
    ]
  },
  {
    id: 'information-tables',
    title: 'Information Tables',
    icon: '📊',
    isExpanded: true,
    items: [
      { id: 'bad-aba', name: 'Bad ABA Numbers', route: '/dashboard/bad-aba' },
      { id: 'bad-employment', name: 'Bad Employment', route: '/dashboard/bad-employment' },
      { id: 'bad-ssn', name: 'Bad SSN', route: '/dashboard/bad-ssn' },
      { id: 'bad-zip', name: 'Bad ZIP Codes', route: '/dashboard/bad-zip' },
      { id: 'do-not-business', name: 'Do Not Do Business', route: '/dashboard/do-not-business' },
      { id: 'ofac-sdn', name: 'OFAC SDN', route: '/dashboard/ofac-sdn' }
    ]
  },
  {
    id: 'ach',
    title: 'ACH Processing',
    icon: '💳',
    isExpanded: true,
    items: [
      { id: 'ach-batch-processing', name: 'ACH Batch Processing Queue', route: '/dashboard/ach-batch-processing' },
      { id: 'ach-batch-renewal', name: 'ACH Batch Renewal Queue', route: '/dashboard/ach-batch-renewal' },
      { id: 'batch-files', name: 'Batch Files Management', route: '/dashboard/batch-files' },
      { id: 'returned-files', name: 'Returned Files Management', route: '/dashboard/returned-files' },
      { id: 'settled-files', name: 'Settled Files Management', route: '/dashboard/settled-files' }
    ]
  },
  {
    id: 'debit-card',
    title: 'Debit Card',
    icon: '💰',
    isExpanded: true,
    items: [
      { id: 'generation-queue', name: 'Generation Queue', route: '/dashboard/generation-queue' },
      { id: 'reattempt-queue', name: 'Reattempt Queue', route: '/dashboard/reattempt-queue' },
      { id: 'status-queue', name: 'Status Queue', route: '/dashboard/status-queue' }
    ]
  }
])

// Computed properties
const filteredMenuGroups = computed(() => {
  if (!searchQuery.value) return menuGroups.value

  return menuGroups.value.map(group => ({
    ...group,
    items: group.items.filter(item =>
      item.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })).filter(group => group.items.length > 0)
})

// Local storage keys
const STORAGE_KEYS = {
  COLLAPSED: 'sidebar-collapsed',
  GROUP_STATES: 'sidebar-group-states'
}

// Methods
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  localStorage.setItem(STORAGE_KEYS.COLLAPSED, JSON.stringify(isCollapsed.value))
  emit('update-collapsed', isCollapsed.value)
}

const toggleSidebar = () => {
  isVisible.value = !isVisible.value
}

const closeSidebar = () => {
  isVisible.value = false
}

const toggleGroup = (groupId: string) => {
  const group = menuGroups.value.find(g => g.id === groupId)
  if (group) {
    group.isExpanded = !group.isExpanded
    saveGroupStates()
  }
}

const handleItemClick = (item: MenuItem) => {
  activeMenuItem.value = item.id
  if (isMobile.value) {
    closeSidebar()
  }
  // Here you would typically navigate to the route
  console.log('Navigate to:', item.route)
}

const saveGroupStates = () => {
  const states = menuGroups.value.reduce((acc, group) => {
    acc[group.id] = group.isExpanded
    return acc
  }, {} as Record<string, boolean>)
  localStorage.setItem(STORAGE_KEYS.GROUP_STATES, JSON.stringify(states))
}

const loadGroupStates = () => {
  const saved = localStorage.getItem(STORAGE_KEYS.GROUP_STATES)
  if (saved) {
    const states = JSON.parse(saved)
    menuGroups.value.forEach(group => {
      if (states.hasOwnProperty(group.id)) {
        group.isExpanded = states[group.id]
      }
    })
  }
}

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768
  if (!isMobile.value) {
    isVisible.value = true
  }
}

// Lifecycle
onMounted(() => {
  // Load saved states
  const savedCollapsed = localStorage.getItem(STORAGE_KEYS.COLLAPSED)
  if (savedCollapsed) {
    isCollapsed.value = JSON.parse(savedCollapsed)
  }
  loadGroupStates()

  // Set up responsive behavior
  checkMobile()
  window.addEventListener('resize', checkMobile)

  // Set initial visibility for mobile
  if (isMobile.value) {
    isVisible.value = false
  } else {
    isVisible.value = true
  }
})

// Watch prop changes to sync with local state
watch(() => props.isCollapsed, (newValue) => {
  isCollapsed.value = newValue
}, { immediate: true })

// Watch route changes to update active item
watch(() => route.path, (newPath) => {
  // Find the active menu item based on current route
  for (const group of menuGroups.value) {
    const activeItem = group.items.find(item => item.route === newPath)
    if (activeItem) {
      activeMenuItem.value = activeItem.id
      // Ensure the group containing the active item is expanded
      group.isExpanded = true
      saveGroupStates()
      break
    }
  }
}, { immediate: true })
</script>
