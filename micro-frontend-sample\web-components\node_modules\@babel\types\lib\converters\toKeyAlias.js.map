{"version": 3, "names": ["_index", "require", "_cloneNode", "_removePropertiesDeep", "to<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "key", "alias", "kind", "increment", "isIdentifier", "name", "isStringLiteral", "JSON", "stringify", "value", "removePropertiesDeep", "cloneNode", "computed", "static", "uid", "Number", "MAX_SAFE_INTEGER"], "sources": ["../../src/converters/toKeyAlias.ts"], "sourcesContent": ["import {\n  isIdentifier,\n  isStringLiteral,\n} from \"../validators/generated/index.ts\";\nimport cloneNode from \"../clone/cloneNode.ts\";\nimport removePropertiesDeep from \"../modifications/removePropertiesDeep.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function toKeyAlias(\n  node: t.Method | t.Property,\n  key: t.Node = node.key,\n): string {\n  let alias;\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.kind`\n  if (node.kind === \"method\") {\n    return toKeyAlias.increment() + \"\";\n  } else if (isIdentifier(key)) {\n    alias = key.name;\n  } else if (isStringLiteral(key)) {\n    alias = JSON.stringify(key.value);\n  } else {\n    alias = JSON.stringify(removePropertiesDeep(cloneNode(key)));\n  }\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.computed`\n  if (node.computed) {\n    alias = `[${alias}]`;\n  }\n\n  // @ts-expect-error todo(flow->ts): maybe add node type check before checking `.static`\n  if (node.static) {\n    alias = `static:${alias}`;\n  }\n\n  return alias;\n}\n\ntoKeyAlias.uid = 0;\n\ntoKeyAlias.increment = function () {\n  if (toKeyAlias.uid >= Number.MAX_SAFE_INTEGER) {\n    return (toKeyAlias.uid = 0);\n  } else {\n    return toKeyAlias.uid++;\n  }\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAIA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;AAGe,SAASG,UAAUA,CAChCC,IAA2B,EAC3BC,GAAW,GAAGD,IAAI,CAACC,GAAG,EACd;EACR,IAAIC,KAAK;EAGT,IAAIF,IAAI,CAACG,IAAI,KAAK,QAAQ,EAAE;IAC1B,OAAOJ,UAAU,CAACK,SAAS,CAAC,CAAC,GAAG,EAAE;EACpC,CAAC,MAAM,IAAI,IAAAC,mBAAY,EAACJ,GAAG,CAAC,EAAE;IAC5BC,KAAK,GAAGD,GAAG,CAACK,IAAI;EAClB,CAAC,MAAM,IAAI,IAAAC,sBAAe,EAACN,GAAG,CAAC,EAAE;IAC/BC,KAAK,GAAGM,IAAI,CAACC,SAAS,CAACR,GAAG,CAACS,KAAK,CAAC;EACnC,CAAC,MAAM;IACLR,KAAK,GAAGM,IAAI,CAACC,SAAS,CAAC,IAAAE,6BAAoB,EAAC,IAAAC,kBAAS,EAACX,GAAG,CAAC,CAAC,CAAC;EAC9D;EAGA,IAAID,IAAI,CAACa,QAAQ,EAAE;IACjBX,KAAK,GAAG,IAAIA,KAAK,GAAG;EACtB;EAGA,IAAIF,IAAI,CAACc,MAAM,EAAE;IACfZ,KAAK,GAAG,UAAUA,KAAK,EAAE;EAC3B;EAEA,OAAOA,KAAK;AACd;AAEAH,UAAU,CAACgB,GAAG,GAAG,CAAC;AAElBhB,UAAU,CAACK,SAAS,GAAG,YAAY;EACjC,IAAIL,UAAU,CAACgB,GAAG,IAAIC,MAAM,CAACC,gBAAgB,EAAE;IAC7C,OAAQlB,UAAU,CAACgB,GAAG,GAAG,CAAC;EAC5B,CAAC,MAAM;IACL,OAAOhB,UAAU,CAACgB,GAAG,EAAE;EACzB;AACF,CAAC", "ignoreList": []}