<template>
  <div class="relative customer-search-container">
    <!-- Search Input Field -->
    <div class="relative">
      <input
        v-model="searchQuery"
        @input="handleSearch"
        @focus="showResults = true"
        type="text"
        placeholder="Search by name, loan number, SSN..."
        class="w-64 px-4 py-2 pl-10 text-sm border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent transition-all duration-200"
      />
      <svg
        class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
      
      <!-- Clear button -->
      <button
        v-if="searchQuery"
        @click="clearSearch"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Search Results Panel -->
    <div
      v-if="showResults && (searchQuery || filteredResults.length > 0)"
      @click.stop
      class="absolute top-full left-0 mt-2 w-[585px] bg-white border border-slate-300 shadow-xl z-50 rounded-lg overflow-hidden"
    >
      <!-- Header -->
      <div class="w-[557px] left-[14px] top-[8px] absolute inline-flex justify-between items-center pt-2">
        <div class="justify-start text-teal-950 text-sm font-normal font-roboto leading-tight">
          {{ filteredResults.length }} Search results
        </div>
        <button
          @click.stop="resetSearch"
          class="text-right justify-start text-cyan-600 text-base font-bold font-roboto leading-loose hover:text-cyan-700 transition-colors"
        >
          Reset
        </button>
      </div>

      <!-- Results List -->
      <div class="mt-12 overflow-y-auto max-h-[400px]">
        <div v-if="filteredResults.length === 0 && searchQuery" class="p-8 text-center">
          <div class="text-gray-500 text-sm">No matching records found</div>
          <div class="text-gray-400 text-xs mt-1">Try searching with different criteria</div>
        </div>
        
        <div v-else>
          <div
            v-for="result in displayedResults"
            :key="result.id"
            @click="selectResult(result)"
            class="py-2.5 px-[14px] bg-white border-b-[0.50px] border-slate-300 flex items-center cursor-pointer hover:bg-gray-50 transition-colors w-full"
          >
            <!-- Search Icon -->
            <div class="w-6 h-6 flex items-center justify-center mr-4">
              <svg class="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>

            <!-- Customer Info - Compact Layout -->
            <div class="flex-1 flex items-center gap-4">
              <div class="flex items-center">
                <span class="text-slate-500 text-sm font-normal font-roboto leading-tight mr-1">Loan #:</span>
                <span class="text-teal-950 text-sm font-normal font-roboto leading-none">{{ result.loanNumber }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-slate-500 text-sm font-normal font-roboto leading-tight mr-1">Name:</span>
                <span class="text-teal-950 text-sm font-normal font-roboto leading-none">{{ result.name }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-slate-500 text-sm font-normal font-roboto leading-tight mr-1">SSN:</span>
                <span class="text-teal-950 text-sm font-normal font-roboto leading-none">{{ result.ssn }}</span>
              </div>
            </div>

            <!-- Arrow Icon -->
            <div class="w-4 h-4 flex items-center justify-center ml-4">
              <img src="@/assets/rectangle_stroke_right.svg" alt="Arrow" class="w-3 h-3" />
            </div>
          </div>
        </div>
      </div>

      <!-- Show More Button -->
      <div v-if="filteredResults.length > visibleItemsCount" class="p-4 border-t border-slate-200 bg-gray-50">
        <button
          @click.stop="showMoreResults"
          class="w-full py-2 text-sm text-cyan-600 font-medium hover:text-cyan-700 transition-colors"
        >
          Show More ({{ Math.min(itemsPerPage, filteredResults.length - visibleItemsCount) }} more results)
        </button>
      </div>
    </div>

    <!-- Overlay to close results -->
    <div
      v-if="showResults && (searchQuery || filteredResults.length > 0)"
      @click="showResults = false"
      class="fixed inset-0 z-40"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface CustomerRecord {
  id: string
  loanNumber: string
  name: string
  ssn: string
  email?: string
  phone?: string
}

// State
const searchQuery = ref('')
const showResults = ref(false)
const visibleItemsCount = ref(10)
const itemsPerPage = 10

// Mock data - 20 customer records
const mockCustomers = ref<CustomerRecord[]>([
  { id: '1', loanNumber: '000057552', name: 'Nicholas Jackson', ssn: '*****2835', email: '<EMAIL>', phone: '(*************' },
  { id: '2', loanNumber: '000057553', name: 'Sarah Williams', ssn: '*****1234', email: '<EMAIL>', phone: '(*************' },
  { id: '3', loanNumber: '000057554', name: 'Michael Brown', ssn: '*****5678', email: '<EMAIL>', phone: '(*************' },
  { id: '4', loanNumber: '000057555', name: 'Emily Davis', ssn: '*****9012', email: '<EMAIL>', phone: '(*************' },
  { id: '5', loanNumber: '000057556', name: 'David Wilson', ssn: '*****3456', email: '<EMAIL>', phone: '(*************' },
  { id: '6', loanNumber: '000057557', name: 'Jessica Miller', ssn: '*****7890', email: '<EMAIL>', phone: '(*************' },
  { id: '7', loanNumber: '000057558', name: 'Christopher Taylor', ssn: '*****2345', email: '<EMAIL>', phone: '(*************' },
  { id: '8', loanNumber: '000057559', name: 'Amanda Anderson', ssn: '*****6789', email: '<EMAIL>', phone: '(*************' },
  { id: '9', loanNumber: '000057560', name: 'James Thomas', ssn: '*****0123', email: '<EMAIL>', phone: '(*************' },
  { id: '10', loanNumber: '000057561', name: 'Ashley Martinez', ssn: '*****4567', email: '<EMAIL>', phone: '(*************' },
  { id: '11', loanNumber: '000057562', name: 'Robert Garcia', ssn: '*****8901', email: '<EMAIL>', phone: '(*************' },
  { id: '12', loanNumber: '000057563', name: 'Michelle Rodriguez', ssn: '*****2345', email: '<EMAIL>', phone: '(*************' },
  { id: '13', loanNumber: '000057564', name: 'Kevin Lee', ssn: '*****6789', email: '<EMAIL>', phone: '(*************' },
  { id: '14', loanNumber: '000057565', name: 'Lisa White', ssn: '*****0123', email: '<EMAIL>', phone: '(*************' },
  { id: '15', loanNumber: '000057566', name: 'Daniel Harris', ssn: '*****4567', email: '<EMAIL>', phone: '(*************' },
  { id: '16', loanNumber: '000057567', name: 'Jennifer Clark', ssn: '*****8901', email: '<EMAIL>', phone: '(*************' },
  { id: '17', loanNumber: '000057568', name: 'Matthew Lewis', ssn: '*****2345', email: '<EMAIL>', phone: '(*************' },
  { id: '18', loanNumber: '000057569', name: 'Stephanie Walker', ssn: '*****6789', email: '<EMAIL>', phone: '(*************' },
  { id: '19', loanNumber: '000057570', name: 'Andrew Hall', ssn: '*****0123', email: '<EMAIL>', phone: '(*************' },
  { id: '20', loanNumber: '000057571', name: 'Rachel Young', ssn: '*****4567', email: '<EMAIL>', phone: '(*************' }
])

// Computed properties
const filteredResults = computed(() => {
  if (!searchQuery.value.trim()) {
    return mockCustomers.value
  }
  
  const query = searchQuery.value.toLowerCase().trim()
  return mockCustomers.value.filter(customer => 
    customer.name.toLowerCase().includes(query) ||
    customer.loanNumber.toLowerCase().includes(query) ||
    customer.ssn.toLowerCase().includes(query)
  )
})

const displayedResults = computed(() => {
  return filteredResults.value.slice(0, visibleItemsCount.value)
})

// Methods
const handleSearch = () => {
  visibleItemsCount.value = itemsPerPage
  showResults.value = true
}

const clearSearch = () => {
  searchQuery.value = ''
  showResults.value = false
  visibleItemsCount.value = itemsPerPage
}

const resetSearch = () => {
  searchQuery.value = ''
  visibleItemsCount.value = itemsPerPage
}

const showMoreResults = () => {
  visibleItemsCount.value += itemsPerPage
}

const selectResult = (customer: CustomerRecord) => {
  console.log('Selected customer:', customer)
  showResults.value = false
  // Navigate to customer dashboard
  // router.push(`/customer/${customer.id}`)
  alert(`Navigating to ${customer.name}'s dashboard (Loan #${customer.loanNumber})`)
}

// Close results when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const searchContainer = target.closest('.customer-search-container')
  if (!searchContainer) {
    showResults.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.font-roboto {
  font-family: 'Roboto', sans-serif;
}
</style>
