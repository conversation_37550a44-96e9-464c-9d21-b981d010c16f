<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Title -->
    <div class="text-sky-950 text-base font-bold font-roboto leading-tight">Denied Reasons Breakdown</div>

    <!-- Show content only when not collapsed -->
    <template v-if="!isCollapsed">
      <!-- By Reason/By Lead Provider Toggle -->
      <div class="flex items-center gap-2.5 mt-4 mb-4">
        <button class="text-cyan-600 text-xs font-semibold font-roboto leading-tight hover:bg-cyan-50 px-2 py-1 rounded transition-colors">By Reason</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">By Lead Provider</button>
      </div>

      <!-- Value -->
      <div class="text-sky-950 text-3xl font-bold font-roboto leading-tight mb-4">{{ employeeStore.currentData.totalValue }}</div>

      <!-- Time Period Selector -->
      <div class="flex items-center gap-2.5 mb-4">
        <button class="text-cyan-600 text-xs font-semibold font-roboto leading-tight hover:bg-cyan-50 px-2 py-1 rounded transition-colors">1 month</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">3 months</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">6 months</button>
        <div class="w-px h-4 bg-slate-300"></div>
        <button class="text-sky-950 text-xs font-normal font-roboto leading-tight hover:bg-gray-50 px-2 py-1 rounded transition-colors">1 year</button>
      </div>

      <!-- Top Divider -->
      <div class="w-full h-px bg-slate-500 mb-4"></div>

      <!-- Items List -->
      <div class="space-y-4">
        <div v-for="(item, index) in employeeStore.currentData.chartData" :key="item.label" class="relative">
          <div class="flex justify-between items-center mb-2">
            <button class="text-sky-950 text-sm font-semibold font-roboto hover:text-cyan-600 transition-colors text-left">{{ index + 1 }}. {{ item.label }}</button>
            <div class="w-7 h-5 bg-sky-950 rounded-full flex items-center justify-center">
              <span class="text-white text-xs font-semibold font-roboto">{{ item.value }}</span>
            </div>
          </div>

          <!-- Progress Bar -->
          <div class="w-full bg-gray-200 rounded-md h-[3px] mb-2">
            <div
              class="h-full rounded-md"
              :style="{
                width: getBarWidth(item.value),
                backgroundColor: item.value > 50 ? '#FFC700' : '#137AB6'
              }"
            ></div>
          </div>

          <!-- Divider -->
          <div v-if="index < employeeStore.currentData.chartData.length - 1" class="w-full h-px bg-slate-300"></div>
        </div>
      </div>

      <!-- Bottom spacing -->
      <div class="flex-1"></div>
      <div class="w-full h-px bg-slate-500 mt-4"></div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWidgetsStore } from '@/stores/widgets'
import { useEmployeeStore } from '@/stores/employee'

// Use stores
const widgetsStore = useWidgetsStore()
const employeeStore = useEmployeeStore()
const isCollapsed = computed(() => widgetsStore.isCollapsed)

// Function to calculate bar width based on value
const getBarWidth = (value: number) => {
  const chartData = employeeStore.currentData.chartData
  const maxValue = Math.max(...chartData.map(item => item.value));
  const percentage = (value / maxValue) * 100;
  return `${percentage}%`;
};
</script>
