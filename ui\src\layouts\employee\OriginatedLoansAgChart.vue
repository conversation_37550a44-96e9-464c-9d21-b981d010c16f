<template>
  <div
    :class="[
      'w-full bg-white rounded-2xl shadow-[0px_2px_12px_0px_rgba(0,0,0,0.05)] border-t-[0.20px] border-slate-300 p-3.5 flex flex-col transition-all duration-300',
      isCollapsed ? 'h-auto' : 'h-[500px]'
    ]"
  >
    <!-- Header -->
    <div class="flex items-start justify-between">
      <div>
        <div class="text-sky-950 text-base font-bold font-roboto leading-tight">Originated Loans</div>
        <div class="text-slate-500 text-xs font-roboto mt-1">{{ data?.dateRange || 'Today' }}</div>
      </div>
      <div class="text-sky-950 bg-slate-100 border border-slate-300 rounded-full px-3 py-1 text-xs font-semibold font-roboto">
        {{ formattedAmount }}
      </div>
    </div>

    <div v-if="!isCollapsed" class="flex-1 grid place-items-center pt-4">
      <AgCharts :options="chartOptions" style="width: 100%; height: 360px" />
      <div class="mt-2 flex items-center gap-6 justify-start w-full">
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :style="{ background: colors.approved }"></span>
          <span class="text-sky-950 text-xs font-roboto">Approved</span>
        </div>
        <div class="flex items-center gap-2">
          <span class="w-3 h-3 rounded-full" :style="{ background: colors.notApproved }"></span>
          <span class="text-sky-950 text-xs font-roboto">Not Approved Yet</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useWidgetsStore } from '@/stores/widgets'
import { useEmployeeStore } from '@/stores/employee'
import type { AgChartOptions } from 'ag-charts-types'

const widgetsStore = useWidgetsStore()
const employeeStore = useEmployeeStore()
const isCollapsed = computed(() => widgetsStore.isCollapsed)

const data = computed(() => employeeStore.currentData.originatedLoans)

const approved = computed(() => data.value?.approved ?? 0)
const notApproved = computed(() => data.value?.notApproved ?? 0)
const total = computed(() => approved.value + notApproved.value)

const formattedAmount = computed(() => {
  const amt = data.value?.totalAmount ?? 0
  return `$${amt.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
})

const chartData = computed(() => {
  const t = total.value || 0
  const ap = approved.value
  const na = notApproved.value
  const apPct = t ? Math.round((ap / t) * 100) : 0
  const naPct = t ? Math.round((na / t) * 100) : 0
  return [
    { type: 'Approved', count: ap, percent: apPct, sectorText: `${ap} (${apPct}%)`, color: colors.approved },
    { type: 'Not Approved Yet', count: na, percent: naPct, sectorText: `${na} (${naPct}%)`, color: colors.notApproved },
  ]
})

const numberFmt = new Intl.NumberFormat('en-US')

const chartOptions = computed<AgChartOptions>(() => ({
  data: chartData.value,
  series: [
    {
      type: 'donut',
      angleKey: 'count',
      calloutLabelKey: 'type',
      sectorLabelKey: 'percent',
      calloutLabel: { enabled: false },
      innerRadiusRatio: 0.64,
      innerLabels: [
        { text: numberFmt.format(total.value), fontSize: 24 },
      ],
      fills: chartData.value.map(d => (d as any).color),
      sectorSpacing: 3,
    },
  ],
  legend: { enabled: false },
}))

const colors = {
  approved: '#137AB6',
  notApproved: '#0F172A',
}
</script>

