{"name": "monarch-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"ag-charts-community": "^12.1.1", "ag-charts-vue3": "^12.1.1", "ag-grid-community": "^34.1.1", "ag-grid-vue3": "^34.1.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.4.1", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "vite": "^5.4.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}