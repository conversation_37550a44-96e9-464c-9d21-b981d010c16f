import { computed } from 'vue'
import { useAppStore } from '@/stores'
import type { Theme } from '@/types'

export function useTheme() {
  const appStore = useAppStore()

  const theme = computed(() => appStore.theme)
  const isDark = computed(() => theme.value === 'dark')

  const toggleTheme = () => {
    const newTheme: Theme = theme.value === 'light' ? 'dark' : 'light'
    appStore.setTheme(newTheme)
  }

  const setTheme = (newTheme: Theme) => {
    appStore.setTheme(newTheme)
  }

  return {
    theme,
    isDark,
    toggleTheme,
    setTheme
  }
}
