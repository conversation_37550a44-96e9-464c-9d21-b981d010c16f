import { fileURLToPath, URL } from 'node:url'
 
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import federation from '@originjs/vite-plugin-federation'
import path, { resolve } from 'node:path'
 
export default defineConfig({
  base: '/Vue/',
  plugins: [
    vue(),
    federation({
      name: 'vueRemote',
      filename: 'remoteEntry.js',
      exposes: {
        './OriginatedLoansCard': './src/layouts/employee/OriginatedLoansCard.vue',
      },
      shared: ['vue', 'vue-router']
    }),
  ],
  build: {
    target: 'esnext',
    cssCodeSplit: true,
    outDir: path.resolve( 'D:\\epic\\EpicLoanSystems.WebSite\\Vue\\'),
    emptyOutDir: true,
    assetsDir: 'assets'
  },
  resolve:{
    alias:{
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
 