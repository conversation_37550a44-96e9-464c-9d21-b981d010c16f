import { defineStore } from "pinia";
import { ref, computed } from "vue";

export interface QueueData {
  id: string;
  name: string;
  categoryId: string;
}

export interface CategoryData {
  id: string;
  name: string;
  queues: QueueData[];
}

export interface ChartItem {
  label: string;
  value: number;
  barColor: string;
  percentage: number;
}

export interface StatsItem {
  label: string;
  value: number;
}

export interface ReasonItem {
  reason: string;
  times: number;
}

export interface ACHProvider {
  id: string;
  name: string;
  loanCount: number;
  percentage: number;
  loanIds: string[];
}

export interface LoanApprovalData {
  providers: ACHProvider[];
  totalLoans: number;
  hasLoans: boolean;
}

export interface OriginatedLoansData {
  approved: number;
  notApproved: number;
  totalAmount: number; // dollar amount for today
  dateRange: "Today" | "Yesterday" | "Last Week" | "Last Month";
}

export interface QueueDataSet {
  chartData: ChartItem[];
  statsItems: StatsItem[];
  topReasons: ReasonItem[];
  totalValue: number;
  loanApprovalData?: LoanApprovalData;
  originatedLoans?: OriginatedLoansData;
}

export const useEmployeeStore = defineStore("employee", () => {
  // State
  const selectedCategoryId = ref("customer-service");
  const selectedQueueId = ref("denied-queue");

  // Mock data for categories and queues
  const categories = ref<CategoryData[]>([
    {
      id: "collections",
      name: "Collections",
      queues: [
        { id: "collections-denied", name: "Denied Queue", categoryId: "collections" },
        { id: "collections-pending", name: "Pending Review", categoryId: "collections" },
        { id: "collections-escalated", name: "Escalated Cases", categoryId: "collections" },
        { id: "collections-recovery", name: "Recovery Queue", categoryId: "collections" },
        { id: "collections-settlement", name: "Settlement Queue", categoryId: "collections" },
      ],
    },
    {
      id: "customer-service",
      name: "Customer Service",
      queues: [
        { id: "denied-queue", name: "Denied Queue", categoryId: "customer-service" },
        { id: "pending-queue", name: "Pending Queue", categoryId: "customer-service" },
        { id: "approved-queue", name: "Loan Approval", categoryId: "customer-service" },
        { id: "review-queue", name: "Review Queue", categoryId: "customer-service" },
        { id: "escalated-queue", name: "Escalated Queue", categoryId: "customer-service" },
      ],
    },
  ]);

  // Mock data sets for different category/queue combinations
  const mockDataSets = ref<Record<string, QueueDataSet>>({
    "collections-collections-denied": {
      chartData: [
        { label: "Payment Default", value: 67, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Insufficient Funds", value: 54, barColor: "bg-yellow-400", percentage: 81 },
        { label: "Account Closed", value: 43, barColor: "bg-cyan-600", percentage: 64 },
        { label: "Dispute Filed", value: 38, barColor: "bg-cyan-600", percentage: 57 },
        { label: "Fraud Alert", value: 29, barColor: "bg-cyan-600", percentage: 43 },
      ],
      statsItems: [
        { label: "Payment Default", value: 12 },
        { label: "Insufficient Funds", value: 8 },
        { label: "Account Closed", value: 5 },
        { label: "Dispute Filed", value: 3 },
      ],
      topReasons: [
        { reason: "Payment Default", times: 92 },
        { reason: "Insufficient Funds", times: 78 },
        { reason: "Account Closed", times: 65 },
        { reason: "Dispute Filed", times: 51 },
        { reason: "Fraud Alert", times: 44 },
      ],
      totalValue: 28,
    },
    "collections-collections-pending": {
      chartData: [
        { label: "Under Review", value: 45, barColor: "bg-cyan-600", percentage: 100 },
        { label: "Documentation Required", value: 38, barColor: "bg-cyan-600", percentage: 84 },
        { label: "Manager Approval", value: 31, barColor: "bg-cyan-600", percentage: 69 },
        { label: "Legal Review", value: 24, barColor: "bg-cyan-600", percentage: 53 },
        { label: "System Processing", value: 18, barColor: "bg-cyan-600", percentage: 40 },
      ],
      statsItems: [
        { label: "Under Review", value: 9 },
        { label: "Documentation Required", value: 6 },
        { label: "Manager Approval", value: 4 },
        { label: "Legal Review", value: 3 },
      ],
      topReasons: [
        { reason: "Under Review", times: 67 },
        { reason: "Documentation Required", times: 54 },
        { reason: "Manager Approval", times: 43 },
        { reason: "Legal Review", times: 32 },
        { reason: "System Processing", times: 25 },
      ],
      totalValue: 22,
    },
    "customer-service-denied-queue": {
      chartData: [
        { label: "Invalid Documents", value: 53, barColor: "bg-yellow-400", percentage: 100 },
        { label: "CL Verify Failed", value: 46, barColor: "bg-cyan-600", percentage: 87 },
        { label: "Unreliable", value: 41, barColor: "bg-cyan-600", percentage: 77 },
        { label: "Bad Contact Info", value: 32, barColor: "bg-cyan-600", percentage: 60 },
        { label: "Bankruptcy", value: 25, barColor: "bg-cyan-600", percentage: 47 },
      ],
      statsItems: [
        { label: "Invalid Documents", value: 8 },
        { label: "Unreliable", value: 4 },
        { label: "Bad Business Info", value: 2 },
        { label: "Bankruptcy", value: 2 },
      ],
      topReasons: [
        { reason: "Invalid Documents", times: 85 },
        { reason: "Unreliable", times: 66 },
        { reason: "CL Verify Failed", times: 57 },
        { reason: "Bad Business Info", times: 43 },
        { reason: "Bankruptcy", times: 38 },
      ],
      totalValue: 17,
    },
    "customer-service-pending-queue": {
      chartData: [
        { label: "Verification Pending", value: 62, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Document Review", value: 48, barColor: "bg-cyan-600", percentage: 77 },
        { label: "Credit Check", value: 35, barColor: "bg-cyan-600", percentage: 56 },
        { label: "Income Verification", value: 28, barColor: "bg-cyan-600", percentage: 45 },
        { label: "Reference Check", value: 21, barColor: "bg-cyan-600", percentage: 34 },
      ],
      statsItems: [
        { label: "Verification Pending", value: 11 },
        { label: "Document Review", value: 7 },
        { label: "Credit Check", value: 5 },
        { label: "Income Verification", value: 4 },
      ],
      topReasons: [
        { reason: "Verification Pending", times: 94 },
        { reason: "Document Review", times: 72 },
        { reason: "Credit Check", times: 58 },
        { reason: "Income Verification", times: 45 },
        { reason: "Reference Check", times: 33 },
      ],
      totalValue: 27,
    },
    // Add missing combinations for Collections category
    "collections-collections-escalated": {
      chartData: [
        { label: "High Priority Cases", value: 58, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Manager Review", value: 42, barColor: "bg-cyan-600", percentage: 72 },
        { label: "Legal Action", value: 35, barColor: "bg-cyan-600", percentage: 60 },
        { label: "Customer Complaint", value: 28, barColor: "bg-cyan-600", percentage: 48 },
        { label: "System Error", value: 21, barColor: "bg-cyan-600", percentage: 36 },
      ],
      statsItems: [
        { label: "High Priority Cases", value: 10 },
        { label: "Manager Review", value: 7 },
        { label: "Legal Action", value: 5 },
        { label: "Customer Complaint", value: 4 },
      ],
      topReasons: [
        { reason: "High Priority Cases", times: 89 },
        { reason: "Manager Review", times: 67 },
        { reason: "Legal Action", times: 54 },
        { reason: "Customer Complaint", times: 41 },
        { reason: "System Error", times: 33 },
      ],
      totalValue: 26,
    },
    "collections-collections-recovery": {
      chartData: [
        { label: "Asset Recovery", value: 61, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Payment Plan", value: 47, barColor: "bg-cyan-600", percentage: 77 },
        { label: "Settlement Offer", value: 39, barColor: "bg-cyan-600", percentage: 64 },
        { label: "Legal Recovery", value: 31, barColor: "bg-cyan-600", percentage: 51 },
        { label: "Write-off", value: 23, barColor: "bg-cyan-600", percentage: 38 },
      ],
      statsItems: [
        { label: "Asset Recovery", value: 11 },
        { label: "Payment Plan", value: 8 },
        { label: "Settlement Offer", value: 6 },
        { label: "Legal Recovery", value: 4 },
      ],
      topReasons: [
        { reason: "Asset Recovery", times: 95 },
        { reason: "Payment Plan", times: 73 },
        { reason: "Settlement Offer", times: 58 },
        { reason: "Legal Recovery", times: 44 },
        { reason: "Write-off", times: 35 },
      ],
      totalValue: 29,
    },
    "collections-collections-settlement": {
      chartData: [
        { label: "Negotiated Settlement", value: 55, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Partial Payment", value: 44, barColor: "bg-cyan-600", percentage: 80 },
        { label: "Full Settlement", value: 36, barColor: "bg-cyan-600", percentage: 65 },
        { label: "Payment Agreement", value: 29, barColor: "bg-cyan-600", percentage: 53 },
        { label: "Hardship Case", value: 22, barColor: "bg-cyan-600", percentage: 40 },
      ],
      statsItems: [
        { label: "Negotiated Settlement", value: 9 },
        { label: "Partial Payment", value: 7 },
        { label: "Full Settlement", value: 5 },
        { label: "Payment Agreement", value: 3 },
      ],
      topReasons: [
        { reason: "Negotiated Settlement", times: 87 },
        { reason: "Partial Payment", times: 69 },
        { reason: "Full Settlement", times: 52 },
        { reason: "Payment Agreement", times: 38 },
        { reason: "Hardship Case", times: 29 },
      ],
      totalValue: 24,
    },
    // Add missing combinations for Customer Service category
    "customer-service-approved-queue": {
      chartData: [
        { label: "Auto Approval", value: 64, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Manual Approval", value: 51, barColor: "bg-yellow-400", percentage: 80 },
        { label: "Conditional Approval", value: 38, barColor: "bg-cyan-600", percentage: 59 },
        { label: "Final Review", value: 30, barColor: "bg-cyan-600", percentage: 47 },
        { label: "Documentation Complete", value: 24, barColor: "bg-cyan-600", percentage: 38 },
      ],
      statsItems: [
        { label: "Auto Approval", value: 13 },
        { label: "Manual Approval", value: 9 },
        { label: "Conditional Approval", value: 6 },
        { label: "Final Review", value: 4 },
      ],
      topReasons: [
        { reason: "Auto Approval", times: 98 },
        { reason: "Manual Approval", times: 76 },
        { reason: "Conditional Approval", times: 61 },
        { reason: "Final Review", times: 47 },
        { reason: "Documentation Complete", times: 34 },
      ],
      totalValue: 32,
      // ACH Provider data for Loan Approval
      originatedLoans: { approved: 21, notApproved: 9, totalAmount: 7000, dateRange: "Today" },
      loanApprovalData: {
        providers: [
          {
            id: "dharma",
            name: "Dharma",
            loanCount: 12,
            percentage: 64,
            loanIds: [
              "LN001234",
              "LN001235",
              "LN001236",
              "LN001237",
              "LN001238",
              "LN001239",
              "LN001240",
              "LN001241",
              "LN001242",
              "LN001243",
              "LN001244",
              "LN001245",
            ],
          },
          {
            id: "leadspedia",
            name: "Leadspedia",
            loanCount: 4,
            percentage: 21,
            loanIds: ["LN002001", "LN002002", "LN002003", "LN002004"],
          },
          {
            id: "ping-tree",
            name: "Ping Tree",
            loanCount: 3,
            percentage: 16,
            loanIds: ["LN003001", "LN003002", "LN003003"],
          },
        ],
        totalLoans: 19,
        hasLoans: true,
      },
    },
    // Empty state version for testing
    "customer-service-approved-queue-empty": {
      chartData: [
        { label: "Auto Approval", value: 0, barColor: "bg-yellow-400", percentage: 100 },
        { label: "Manual Approval", value: 0, barColor: "bg-yellow-400", percentage: 80 },
        { label: "Conditional Approval", value: 0, barColor: "bg-cyan-600", percentage: 59 },
        { label: "Final Review", value: 0, barColor: "bg-cyan-600", percentage: 47 },
        { label: "Documentation Complete", value: 0, barColor: "bg-cyan-600", percentage: 38 },
      ],
      statsItems: [
        { label: "Auto Approval", value: 0 },
        { label: "Manual Approval", value: 0 },
        { label: "Conditional Approval", value: 0 },
        { label: "Final Review", value: 0 },
      ],
      topReasons: [
        { reason: "Auto Approval", times: 0 },
        { reason: "Manual Approval", times: 0 },
        { reason: "Conditional Approval", times: 0 },
        { reason: "Final Review", times: 0 },
        { reason: "Documentation Complete", times: 0 },
      ],
      totalValue: 0,
      loanApprovalData: {
        providers: [],
        totalLoans: 0,
        hasLoans: false,
      },
    },
    "customer-service-review-queue": {
      chartData: [
        { label: "Quality Review", value: 49, barColor: "bg-cyan-600", percentage: 100 },
        { label: "Risk Assessment", value: 41, barColor: "bg-cyan-600", percentage: 84 },
        { label: "Compliance Check", value: 34, barColor: "bg-cyan-600", percentage: 69 },
        { label: "Manager Review", value: 27, barColor: "bg-cyan-600", percentage: 55 },
        { label: "Final Verification", value: 20, barColor: "bg-cyan-600", percentage: 41 },
      ],
      statsItems: [
        { label: "Quality Review", value: 8 },
        { label: "Risk Assessment", value: 6 },
        { label: "Compliance Check", value: 5 },
        { label: "Manager Review", value: 3 },
      ],
      topReasons: [
        { reason: "Quality Review", times: 74 },
        { reason: "Risk Assessment", times: 59 },
        { reason: "Compliance Check", times: 46 },
        { reason: "Manager Review", times: 35 },
        { reason: "Final Verification", times: 26 },
      ],
      totalValue: 22,
    },
    "customer-service-escalated-queue": {
      chartData: [
        { label: "Customer Complaint", value: 56, barColor: "bg-yellow-400", percentage: 100 },
        { label: "System Issue", value: 43, barColor: "bg-cyan-600", percentage: 77 },
        { label: "Process Error", value: 37, barColor: "bg-cyan-600", percentage: 66 },
        { label: "Supervisor Review", value: 29, barColor: "bg-cyan-600", percentage: 52 },
        { label: "Policy Exception", value: 22, barColor: "bg-cyan-600", percentage: 39 },
      ],
      statsItems: [
        { label: "Customer Complaint", value: 10 },
        { label: "System Issue", value: 7 },
        { label: "Process Error", value: 5 },
        { label: "Supervisor Review", value: 4 },
      ],
      topReasons: [
        { reason: "Customer Complaint", times: 91 },
        { reason: "System Issue", times: 68 },
        { reason: "Process Error", times: 53 },
        { reason: "Supervisor Review", times: 39 },
        { reason: "Policy Exception", times: 28 },
      ],
      totalValue: 26,
    },
  });

  // Computed properties
  const selectedCategory = computed(() =>
    categories.value.find((cat) => cat.id === selectedCategoryId.value)
  );

  const selectedQueue = computed(() =>
    selectedCategory.value?.queues.find((queue) => queue.id === selectedQueueId.value)
  );

  const availableQueues = computed(() => selectedCategory.value?.queues || []);

  const currentDataKey = computed(() => `${selectedCategoryId.value}-${selectedQueueId.value}`);

  const currentData = computed(() => {
    const key = currentDataKey.value;
    return (
      mockDataSets.value[key as keyof typeof mockDataSets.value] ||
      mockDataSets.value["customer-service-denied-queue"]
    );
  });

  const currentLoanApprovalData = computed(() => {
    return (
      currentData.value.loanApprovalData || {
        providers: [],
        totalLoans: 0,
        hasLoans: false,
      }
    );
  });

  // Widget quick filter state (for ACH Provider from widgets)
  type WidgetFilter = { key: "achProvider"; label: string; value: string } | null;
  const widgetFilter = ref<WidgetFilter>(null);
  const setWidgetFilter = (f: WidgetFilter) => {
    widgetFilter.value = f;
  };
  const clearWidgetFilter = () => {
    widgetFilter.value = null;
  };

  // Actions
  const setCategory = (categoryId: string) => {
    selectedCategoryId.value = categoryId;
    // Reset to first queue of the selected category
    const category = categories.value.find((cat) => cat.id === categoryId);
    if (category && category.queues.length > 0) {
      selectedQueueId.value = category.queues[0].id;
    }
  };

  const setQueue = (queueId: string) => {
    selectedQueueId.value = queueId;
  };

  // Generate random data for demonstration
  const generateRandomData = () => {
    const currentSet = currentData.value;

    // Randomize chart data values
    const newChartData = currentSet.chartData.map((item) => ({
      ...item,
      value: Math.floor(Math.random() * 70) + 20, // Random value between 20-90
    }));

    // Randomize stats items
    const newStatsItems = currentSet.statsItems.map((item) => ({
      ...item,
      value: Math.floor(Math.random() * 15) + 1, // Random value between 1-15
    }));

    // Randomize top reasons
    const newTopReasons = currentSet.topReasons.map((item) => ({
      ...item,
      times: Math.floor(Math.random() * 100) + 20, // Random value between 20-120
    }));

    // Update the mock data set
    const key = currentDataKey.value as keyof typeof mockDataSets.value;
    if (mockDataSets.value[key]) {
      mockDataSets.value[key] = {
        ...mockDataSets.value[key],
        chartData: newChartData,
        statsItems: newStatsItems,
        topReasons: newTopReasons,
        totalValue: Math.floor(Math.random() * 50) + 10, // Random total between 10-60
      };
    }
  };

  return {
    // State
    selectedCategoryId,
    selectedQueueId,
    categories,

    // Computed
    selectedCategory,
    selectedQueue,
    availableQueues,
    currentData,
    currentLoanApprovalData,
    widgetFilter,

    // Actions
    setCategory,
    setQueue,
    generateRandomData,
    setWidgetFilter,
    clearWidgetFilter,
  };
});
