<template>
  <div class="flex flex-col pt-2.5 mt-3.5 w-full bg-white rounded-2xl">
    <div class="flex flex-wrap gap-5 justify-between w-full text-sm leading-none px-3.5">
      <h2 class="my-auto font-bold text-teal-950">Denied Queue</h2>
      <div class="flex gap-3 items-center font-medium pr-3.5">
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/d47df643e9e2501f1df3b79c4d5121f7569e375f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch w-6 aspect-square"
          alt="Customize icon"
        />
        <button class="self-stretch my-auto text-cyan-600">Customize</button>
        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"></div>
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/2c568c72859adff17e25d94611764fbec135e2be?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch w-6 aspect-square"
          alt="Filter icon"
        />
        <button class="self-stretch my-auto text-sky-950">Filters (0)</button>
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/6d53b892b13b69050644d37b0512bc56ef052785?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.56]"
          alt="Dropdown arrow"
        />
      </div>
    </div>

    <!-- Table content (always visible) -->
      <div class="overflow-x-auto mt-2.5 bg-white">
        <table class="w-full min-w-[1200px]">
          <thead>
            <tr class="bg-slate-50">
              <th v-for="column in columns" :key="column.key"
                  class="overflow-hidden px-4 text-xs font-bold leading-6 shadow-sm h-[35px] text-slate-500 text-left whitespace-nowrap">
                <div class="flex items-center py-2 w-full min-h-[35px]">
                  <span class="flex-1 shrink self-stretch my-auto basis-0">{{ column.title }}</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in tableData" :key="index" class="border-b border-gray-100">
              <td v-for="column in columns" :key="column.key"
                  class="px-4 py-2 min-h-[25px] whitespace-nowrap">
                <div class="flex gap-2.5 items-center text-sm leading-loose"
                     :class="getColumnClass(column.key)">
                  <span>{{ row[column.key] }}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

    <div class="shrink-0 mt-4 h-0 border border-solid border-slate-300"></div>

    <div class="flex flex-col sm:flex-row gap-4 justify-between items-center py-4 px-4 bg-white">
      <div class="flex flex-col sm:flex-row gap-4 items-center">
        <!-- Pagination Controls -->
        <div class="flex gap-2 items-center">
          <button class="flex justify-center items-center w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </svg>
          </button>
          <span class="text-sm text-sky-950 font-medium">1 / 1</span>
          <button class="flex justify-center items-center w-8 h-8 text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
            </svg>
          </button>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-4 items-center">
          <button class="flex gap-2 items-center text-cyan-600 hover:text-cyan-700 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
            <span class="text-sm font-medium">Refresh</span>
          </button>

          <button class="flex gap-2 items-center text-cyan-600 hover:text-cyan-700 transition-colors">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2v9.67z"/>
            </svg>
            <span class="text-sm font-medium">Export</span>
          </button>
        </div>
      </div>

        <div class="flex items-center gap-4 text-sm leading-none text-right text-sky-950">
          <div class="shrink-0 w-px h-4 border border-solid border-slate-300"></div>
          <span>Displaying 1 - 8 of 8</span>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
interface TableColumn {
  key: string;
  title: string;
  sortIcon: string;
  iconClass: string;
}

interface TableRow {
  [key: string]: string;
}

const columns: TableColumn[] = [
  { key: 'dateAdded', title: 'Date Added', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/076dd0c24877a627d091885b85d6079df2c57c1f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[139px]' },
  { key: 'firstName', title: 'First Name', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[71px]' },
  { key: 'lastName', title: 'Last Name', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[71px]' },
  { key: 'store', title: 'STORE', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[71px]' },
  { key: 'language', title: 'Language', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/ee0fa0cdcdcfb9b71dea5428e2c20ee7bc97bf57?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[68px]' },
  { key: 'ssn', title: 'SSN', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[71px]' },
  { key: 'loanNumber', title: 'Loan #', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-full' },
  { key: 'emailAddress', title: 'Email Address', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/0e485042aa47e4e043acdf22ecf8d07ac9ef4803?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[131px]' },
  { key: 'state', title: 'State', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/a5ac6405d23828e1072cc69e10a8b1c52a2c983c?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[47px]' },
  { key: 'leadProvider', title: 'Lead Provider', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/056b0fb6fdafcc59bef11e9baf4e2dd19f4027d7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-full' },
  { key: 'denialReason', title: 'Denial Reason', sortIcon: 'https://cdn.builder.io/api/v1/image/assets/TEMP/91171f1678d90195d540853a49deeb2ca342141a?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde', iconClass: 'w-[142px]' }
];

const tableData: TableRow[] = [
  {
    dateAdded: '11/04/2022, 10:16AM',
    firstName: 'Jennifer',
    lastName: 'Moore',
    store: 'ST901-ID71',
    language: 'English',
    ssn: '**********',
    loanNumber: '000026543',
    emailAddress: '<EMAIL>',
    state: 'TX',
    leadProvider: '818 OP',
    denialReason: 'Invalid Documents'
  },
  {
    dateAdded: '11/04/2022, 10:16AM',
    firstName: 'Jennifer',
    lastName: 'Moore',
    store: 'ST901-ID71',
    language: 'English',
    ssn: '**********',
    loanNumber: '000026543',
    emailAddress: '<EMAIL>',
    state: 'FL',
    leadProvider: '818 OP',
    denialReason: 'Unreliable'
  },
  {
    dateAdded: '11/04/2022, 10:16AM',
    firstName: 'Jennifer',
    lastName: 'Moore',
    store: 'ST901-ID71',
    language: 'English',
    ssn: '**********',
    loanNumber: '000026543',
    emailAddress: '<EMAIL>',
    state: 'FL',
    leadProvider: '818 OP',
    denialReason: 'Invalid Documents'
  },
  {
    dateAdded: '11/04/2022, 10:16AM',
    firstName: 'Jennifer',
    lastName: 'Moore',
    store: 'ST901-ID71',
    language: 'English',
    ssn: '**********',
    loanNumber: '000026543',
    emailAddress: '<EMAIL>',
    state: 'TX',
    leadProvider: '818 OP',
    denialReason: 'Unreliable'
  }
];

const getColumnClass = (columnKey: string): string => {
  switch (columnKey) {
    case 'firstName':
    case 'lastName':
      return 'text-sky-600 font-semibold whitespace-nowrap';
    case 'state':
      return 'text-teal-950 whitespace-nowrap';
    default:
      return 'text-teal-950';
  }
};
</script>
