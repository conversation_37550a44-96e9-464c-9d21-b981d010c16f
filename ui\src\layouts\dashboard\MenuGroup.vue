<template>
  <div class="menu-group">
    <!-- Group Header -->
    <button
      @click="handleToggle"
      :class="[
        'group-header w-full flex items-center justify-between p-2 rounded-lg transition-all duration-200',
        'hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:ring-opacity-50',
        isCollapsed ? 'justify-center' : 'justify-between'
      ]"
      :aria-expanded="group.isExpanded"
      :aria-controls="`group-${group.id}`"
    >
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <span class="text-lg flex-shrink-0" :title="isCollapsed ? group.title : ''">
          {{ group.icon }}
        </span>
        <span
          v-if="!isCollapsed"
          class="text-sm font-semibold text-gray-700 group-hover:text-cyan-600 transition-colors duration-200 truncate"
        >
          {{ group.title }}
        </span>
      </div>
      <svg
        v-if="!isCollapsed"
        :class="[
          'w-4 h-4 text-gray-500 transition-transform duration-200 flex-shrink-0',
          group.isExpanded ? 'rotate-90' : ''
        ]"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </button>

    <!-- Group Items -->
    <div
      v-if="!isCollapsed"
      :id="`group-${group.id}`"
      :class="[
        'group-items overflow-hidden transition-all duration-300 ease-in-out',
        group.isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      ]"
    >
      <ul class="mt-1 space-y-0.5 pl-3">
        <li
          v-for="item in group.items"
          :key="item.id"
          @click="handleItemClick(item)"
          :class="[
            'menu-item cursor-pointer px-2 py-1.5 rounded-md text-sm transition-all duration-200',
            'hover:bg-cyan-50 hover:text-cyan-700 hover:translate-x-1',
            activeItem === item.id
              ? 'bg-cyan-100 text-cyan-800 font-medium border-l-4 border-cyan-500'
              : 'text-gray-600 hover:text-cyan-700'
          ]"
          :aria-current="activeItem === item.id ? 'page' : undefined"
          role="menuitem"
        >
          <span class="block">{{ item.name }}</span>
        </li>
      </ul>
    </div>

    <!-- Collapsed state tooltip items -->
    <div
      v-if="isCollapsed && group.isExpanded"
      class="absolute left-16 top-0 z-50 bg-white shadow-lg rounded-lg border border-gray-200 py-2 min-w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200"
    >
      <div class="px-3 py-2 border-b border-gray-100">
        <span class="text-sm font-semibold text-gray-700">{{ group.title }}</span>
      </div>
      <ul class="py-1">
        <li
          v-for="item in group.items"
          :key="item.id"
          @click="handleItemClick(item)"
          :class="[
            'menu-item cursor-pointer px-3 py-2 text-sm transition-colors duration-200',
            'hover:bg-cyan-50 hover:text-cyan-700',
            activeItem === item.id 
              ? 'bg-cyan-100 text-cyan-800 font-medium' 
              : 'text-gray-600'
          ]"
          role="menuitem"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
interface MenuItem {
  id: string
  name: string
  route: string
}

interface MenuGroup {
  id: string
  title: string
  icon: string
  isExpanded: boolean
  items: MenuItem[]
}

interface Props {
  group: MenuGroup
  isCollapsed: boolean
  activeItem: string
}

interface Emits {
  (e: 'toggle', groupId: string): void
  (e: 'item-click', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleToggle = () => {
  if (!props.isCollapsed) {
    emit('toggle', props.group.id)
  }
}

const handleItemClick = (item: MenuItem) => {
  emit('item-click', item)
}
</script>

<style scoped>
.menu-group {
  position: relative;
}

.group-header:hover .group-items {
  /* Ensure hover state works properly */
}

.menu-item {
  position: relative;
}

.menu-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  background-color: #0891b2;
  transition: all 0.2s ease;
}

.menu-item:hover::before {
  width: 3px;
  height: 100%;
}

/* Smooth animations */
.group-items {
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* Focus styles for accessibility */
.group-header:focus,
.menu-item:focus {
  outline: 2px solid #0891b2;
  outline-offset: 2px;
}
</style>
