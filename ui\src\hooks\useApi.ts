import { ref } from 'vue'
import type { ApiResponse } from '@/types'

export function useApi() {
  const loading = ref(false)
  const error = ref<string | null>(null)

  const request = async <T>(
    url: string,
    options?: RequestInit
  ): Promise<ApiResponse<T> | null> => {
    loading.value = true
    error.value = null

    try {
      const response = await fetch(url, options)
      const data = await response.json()
      
      if (!response.ok) {
        throw new Error(data.message || 'API request failed')
      }

      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      return null
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    error,
    request
  }
}
