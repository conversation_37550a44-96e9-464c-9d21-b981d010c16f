import { ref } from "vue";
import { defineStore } from "pinia";

export const useWidgetsStore = defineStore("widgets", () => {
  // State
  const isCollapsed = ref(false);

  // Actions
  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
  };

  const setCollapsed = (collapsed: boolean) => {
    isCollapsed.value = collapsed;
  };

  return {
    isCollapsed,
    toggleCollapse,
    setCollapsed,
  };
});
