<template>
  <main class="w-full text-white">
    <div class="py-3 px-4 w-full border-t-0 bg-slate-800 border-slate-300">
      <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center w-full gap-4">
        <!-- Left Section: My Contact Info -->
        <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-6">
          <h1 class="text-sm font-bold leading-none whitespace-nowrap">
            My Contact Info
          </h1>
          <div class="flex items-center gap-3 text-xs font-medium leading-loose">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/680ec530bc1dd0a183574a7c2d29cd8bc1ecde55?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 w-14 rounded-lg aspect-[2.33]"
              alt="Contact info icon"
            />
            <div class="flex gap-2.5 px-2 py-0.5 bg-cyan-600 rounded-lg">
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/c750e6d36e264e6815c02506ce39fcead80e83d6?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
                class="object-contain shrink-0 my-auto w-3.5 aspect-square"
                alt="Email icon"
              />
              <span class="text-xs truncate">
                <EMAIL>
              </span>
            </div>
          </div>
        </div>

        <!-- Right Section: Auto Refresh, All Stores, Filters -->
        <div class="flex flex-wrap items-center gap-2 sm:gap-3 text-sm font-medium leading-none">
          <div class="flex items-center gap-2">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/37d333c57b3ff3a042180766f382f41b4ac5ec9b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 w-7 aspect-square"
              alt="Auto refresh icon"
            />
            <span class="whitespace-nowrap">Auto Refresh</span>
          </div>
          <div class="w-px h-4 border border-solid border-slate-300 hidden sm:block"></div>
          <div class="relative">
            <button
              @click="toggleStoresDropdown"
              class="flex items-center gap-2 hover:opacity-80 transition-opacity"
            >
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/b1bcc79b456352694b231a0fd6a31bc5840f77da?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
                class="object-contain shrink-0 w-6 aspect-square"
                alt="Stores icon"
              />
              <span class="whitespace-nowrap">{{ selectedStore }}</span>
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
                class="object-contain shrink-0 w-3.5 aspect-[1.56]"
                alt="Dropdown arrow"
              />
            </button>

            <!-- Dropdown Menu -->
            <div
              v-if="showStoresDropdown"
              class="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
            >
              <button
                @click="selectStore('default')"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg"
              >
                Default
              </button>
              <button
                @click="selectStore('Unused Store 2X')"
                class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 last:rounded-b-lg"
              >
                Unused Store 2X
              </button>
            </div>
          </div>
          <div class="w-px h-4 border border-solid border-slate-300 hidden sm:block"></div>
          <div class="flex items-center gap-2">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/8a917a399fa95e2eb728c43c9343d08259668917?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 w-6 aspect-square"
              alt="Filters icon"
            />
            <span class="whitespace-nowrap">Filters (0)</span>
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 w-3.5 aspect-[1.56]"
              alt="Dropdown arrow"
            />
          </div>
        </div>
      </div>

      <nav class="mt-2 w-full text-xs leading-loose overflow-x-auto">
        <div class="flex items-center gap-2.5 min-w-max pb-2">
          <button
            @click="setActiveTab('lead-analysis')"
            :class="[
              'px-3 py-1 rounded-lg font-semibold transition-colors duration-200',
              activeTab === 'lead-analysis'
                ? 'bg-cyan-600 text-white'
                : 'text-cyan-600 hover:bg-cyan-100'
            ]"
          >
            Lead Analysis
          </button>
          <div class="w-px h-4 border border-solid border-slate-300"></div>
          <button
            @click="setActiveTab('new-loan-summary')"
            :class="[
              'px-3 py-1 rounded-lg transition-colors duration-200',
              activeTab === 'new-loan-summary'
                ? 'bg-cyan-600 text-white font-semibold'
                : 'text-white hover:text-cyan-300'
            ]"
          >
            New Loan Summary
          </button>
          <div class="w-px h-4 border border-solid border-slate-300"></div>
          <button
            @click="setActiveTab('cpa-lead-provider')"
            :class="[
              'px-3 py-1 rounded-lg transition-colors duration-200',
              activeTab === 'cpa-lead-provider'
                ? 'bg-cyan-600 text-white font-semibold'
                : 'text-white hover:text-cyan-300'
            ]"
          >
            CPA by Lead Provider
          </button>
          <div class="w-px h-4 border border-solid border-slate-300"></div>
          <button
            @click="setActiveTab('promise-to-pay')"
            :class="[
              'px-3 py-1 rounded-lg transition-colors duration-200',
              activeTab === 'promise-to-pay'
                ? 'bg-cyan-600 text-white font-semibold'
                : 'text-white hover:text-cyan-300'
            ]"
          >
            Promise to Pay Summary
          </button>
          <div class="w-px h-4 border border-solid border-slate-300"></div>
          <button
            @click="setActiveTab('collection-age-bucket')"
            :class="[
              'px-3 py-1 rounded-lg transition-colors duration-200',
              activeTab === 'collection-age-bucket'
                ? 'bg-cyan-600 text-white font-semibold'
                : 'text-white hover:text-cyan-300'
            ]"
          >
            Collection Age Bucket
          </button>
          <div class="w-px h-4 border border-solid border-slate-300"></div>
          <button
            @click="setActiveTab('defaulted-vs-payments')"
            :class="[
              'px-3 py-1 rounded-lg transition-colors duration-200',
              activeTab === 'defaulted-vs-payments'
                ? 'bg-cyan-600 text-white font-semibold'
                : 'text-white hover:text-cyan-300'
            ]"
          >
            Defaulted vs. Payments Report
          </button>
        </div>
      </nav>
    </div>
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

// Active tab state
const activeTab = ref('lead-analysis')

// Stores dropdown state
const selectedStore = ref('All Stores')
const showStoresDropdown = ref(false)

// Watch route changes to update selected store
watch(() => route.path, (newPath) => {
  if (newPath === '/employee-denied-queue') {
    selectedStore.value = 'Unused Store 2X'
  } else if (newPath === '/dashboard') {
    selectedStore.value = 'All Stores'
  }
}, { immediate: true })

// Method to set active tab
const setActiveTab = (tabId: string) => {
  activeTab.value = tabId
}

// Toggle stores dropdown
const toggleStoresDropdown = () => {
  showStoresDropdown.value = !showStoresDropdown.value
}

// Select store and handle navigation
const selectStore = (store: string) => {
  showStoresDropdown.value = false

  if (store === 'Unused Store 2X') {
    selectedStore.value = 'Unused Store 2X'
    router.push('/employee-denied-queue')
  } else if (store === 'default') {
    selectedStore.value = 'All Stores'
    router.push('/dashboard')
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const dropdown = target.closest('.relative')
  if (!dropdown) {
    showStoresDropdown.value = false
  }
}

// Add event listener for clicking outside when component mounts
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
