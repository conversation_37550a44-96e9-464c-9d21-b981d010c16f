<template>
  <div class="w-full bg-white rounded-2xl">
    <!-- Widget filter pill area -->
    <div v-if="employeeStore.widgetFilter" class="px-3.5 pt-3">
      <span class="inline-flex items-center bg-cyan-50 text-cyan-800 px-2 py-1 rounded text-xs font-medium">
        {{ employeeStore.widgetFilter.label }}: {{ employeeStore.widgetFilter.value }}
        <button class="ml-2 text-cyan-700" @click="employeeStore.clearWidgetFilter()">×</button>
      </span>
    </div>

    <div class="ag-theme-quartz w-full text-sky-950" style="min-height: 420px">
      <AgGridVue
        :columnDefs="columnDefs"
        :rowData="filteredRowData"
        :defaultColDef="defaultColDef"
        :pagination="true"
        :paginationPageSize="13"
        :rowSelection="'multiple'"
        :suppressRowClickSelection="true"
        domLayout="autoHeight"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { AgGridVue } from 'ag-grid-vue3'
import type { ColDef } from 'ag-grid-community'
import { useEmployeeStore } from '@/stores/employee'

const employeeStore = useEmployeeStore()

// Exact columns per your Pending Applications screenshot
const columnDefs = ref<ColDef[]>([
  { field: 'select', headerName: '', width: 40, checkboxSelection: true, headerCheckboxSelection: true, pinned: 'left' },
  { field: 'dateAdded', headerName: 'Date Added' },
  { field: 'failedApproval', headerName: 'Failed Approval' },
  { field: 'firstName', headerName: 'First Name', cellClass: 'text-sky-600 font-semibold' },
  { field: 'lastName', headerName: 'Last Name', cellClass: 'text-sky-600 font-semibold' },
  { field: 'language', headerName: 'Language' },
  { field: 'loanNumber', headerName: 'Loan #' },
  { field: 'ssn', headerName: 'SSN' },
  { field: 'store', headerName: 'STORE' },
  { field: 'daysOld', headerName: 'Days Old' },
  { field: 'esign', headerName: 'E-Sign' },
  { field: 'esignature', headerName: 'E-Signature' },
])

const defaultColDef: ColDef = {
  sortable: true,
  resizable: true,
  filter: true,
  minWidth: 80,
}

// Mock data including achProviderName for widget filter linkage
const rowData = ref([
  { select: '', dateAdded: '11/04/2022, 10:16AM', failedApproval: '-', firstName: 'Jennifer', lastName: 'Moore', language: 'English', loanNumber: '000026543', ssn: 'XXXXX4983174112', store: 'ST901-ID71', daysOld: 0, esign: 'Not Signed', esignature: '-', achProviderName: 'Dharma' },
  { select: '', dateAdded: '11/04/2022, 10:16AM', failedApproval: '-', firstName: 'Jennifer', lastName: 'Moore', language: 'English', loanNumber: '000026543', ssn: 'XXXXX4983174112', store: 'ST901-ID71', daysOld: 0, esign: 'Not Signed', esignature: '-', achProviderName: 'Stripe' },
  { select: '', dateAdded: '11/04/2022, 10:16AM', failedApproval: '-', firstName: 'Jennifer', lastName: 'Moore', language: 'English', loanNumber: '000026543', ssn: 'XXXXX4983174112', store: 'ST901-ID71', daysOld: 0, esign: 'Not Signed', esignature: '-', achProviderName: 'Payment Cloud' },
  { select: '', dateAdded: '11/04/2022, 10:16AM', failedApproval: '-', firstName: 'Jennifer', lastName: 'Moore', language: 'English', loanNumber: '000026543', ssn: 'XXXXX4983174112', store: 'ST901-ID71', daysOld: 0, esign: 'Not Signed', esignature: '-', achProviderName: 'Dharma' },
])

const filteredRowData = computed(() => {
  const f = employeeStore.widgetFilter
  if (!f || f.key !== 'achProvider') return rowData.value
  return rowData.value.filter(r => (r as any).achProviderName === f.value)
})
</script>

<style scoped>
</style>

