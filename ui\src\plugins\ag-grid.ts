import type { App } from 'vue'
import { ModuleRegistry, AllCommunityModule } from 'ag-grid-community'
import { AgGridVue } from 'ag-grid-vue3'

export function registerAgGrid(app: App) {
  // Register AG Grid Community Modules ONCE at app startup
  ModuleRegistry.registerModules([AllCommunityModule])

  // Register component globally so <AgGridVue> can be used anywhere
  app.component('AgGridVue', AgGridVue)
}

