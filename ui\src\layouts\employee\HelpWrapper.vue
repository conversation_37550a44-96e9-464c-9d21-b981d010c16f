<template>
  <div class="w-full">
    <!-- Help Navigation Header -->
    <div class="py-3 px-6 w-full border-t-0 bg-slate-800 border-slate-300">
      <div class="flex items-center justify-between w-full">
        <!-- Left Section: Employee Dashboard -->
        <div class="flex items-center gap-8">
          <h1 class="text-lg font-bold text-white whitespace-nowrap">
            Employee Dashboard
          </h1>
          <nav class="flex flex-auto gap-2.5 items-center font-medium text-cyan-500">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/d400eb976c922c99bad2cd919136d3aceda18ed7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Customize icon"
            />
            <button
              @click="navigateToCustomize"
              class="self-stretch my-auto hover:text-cyan-400 transition-colors"
              :class="{ 'text-cyan-300': props.currentView === 'customize' }"
            >
              Customize
            </button>
            <div
              class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
              role="separator"
            ></div>
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/7a05d92f70cf51d839867d2fe6d49070fd3373dd?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Help icon"
            />
            <button
              @click="navigateToHelp"
              class="self-stretch my-auto hover:text-cyan-400 transition-colors"
              :class="{ 'text-cyan-300': props.currentView === 'help' || !props.currentView }"
            >
              Help
            </button>
            <div
              class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
              role="separator"
            ></div>
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/c58da17850dd94a2cc9da6f7712ca837ad8d3b9a?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 self-stretch w-6 aspect-square"
              alt="Notices icon"
            />
            <span class="self-stretch my-auto text-cyan-400">Notices (2)</span>
          </nav>
        </div>

        <!-- Right Section: Selected Store -->
        <div class="flex items-center gap-2 text-white relative" data-dropdown="store">
          <span class="text-sm">Selected Store:</span>
          <button
            @click="toggleStoreDropdown"
            class="flex items-center gap-2 font-medium hover:text-gray-200 transition-colors"
          >
            <span>{{ selectedStore }}</span>
            <svg class="w-3 h-3 transform transition-transform duration-200" :class="{ 'rotate-180': showStoreDropdown }" fill="currentColor" viewBox="0 0 24 24">
              <path d="M7 10l5 5 5-5z"/>
            </svg>
          </button>

          <!-- Store Dropdown Menu -->
          <div
            v-if="showStoreDropdown"
            class="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
          >
            <button
              @click="selectStore('All Stores')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg"
            >
              Default
            </button>
            <button
              @click="selectStore('Unused Store 2X')"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 last:rounded-b-lg"
            >
              Unused Store 2X
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Dynamic Content Area -->
    <!-- Help Content (Main Help Page) -->
    <HelpContent
      v-if="props.currentView === 'help' || !props.currentView"
      @navigate-to-bulletin="handleBulletinNavigation"
      @navigate-to-customize="navigateToCustomize"
      @navigate-to-new-bulletin="navigateToNewBulletin"
    />

    <!-- Bulletin Detail -->
    <BulletinDetail
      v-else-if="props.currentView === 'bulletin-detail'"
      :bulletin-id="props.selectedBulletinId || null"
      @navigate-back="navigateToHelp"
    />

    <!-- Help Customize -->
    <HelpCustomize
      v-else-if="props.currentView === 'customize'"
      @navigate-to-new-bulletin="navigateToNewBulletin"
    />

    <!-- New Bulletin -->
    <NewBulletin
      v-else-if="props.currentView === 'new-bulletin'"
      @navigate-back="navigateToCustomize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import HelpContent from './HelpContent.vue'
import BulletinDetail from './BulletinDetail.vue'
import HelpCustomize from './HelpCustomize.vue'
import NewBulletin from './NewBulletin.vue'

// Router
const router = useRouter()

// Props
const props = defineProps<{
  currentView?: 'help' | 'bulletin-detail' | 'customize' | 'new-bulletin'
  selectedBulletinId?: number | null
}>()

// Define emits for parent component
const emit = defineEmits(['navigate-to-help', 'navigate-to-customize', 'navigate-to-bulletin', 'navigate-to-new-bulletin'])

// Store dropdown state
const showStoreDropdown = ref(false)
const selectedStore = ref('Unused Store 2X')

// Navigation methods
const navigateToHelp = () => {
  emit('navigate-to-help')
}

const navigateToCustomize = () => {
  emit('navigate-to-customize')
}

const navigateToNewBulletin = () => {
  emit('navigate-to-new-bulletin')
}

const handleBulletinNavigation = (bulletinId: number) => {
  emit('navigate-to-bulletin', bulletinId)
}

// Store dropdown methods
const toggleStoreDropdown = () => {
  showStoreDropdown.value = !showStoreDropdown.value
}

const selectStore = (store: string) => {
  showStoreDropdown.value = false

  if (store === 'All Stores') {
    selectedStore.value = 'All Stores'
    router.push('/dashboard')
  } else if (store === 'Unused Store 2X') {
    selectedStore.value = 'Unused Store 2X'
    // Stay on current page
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const storeDropdown = target.closest('[data-dropdown="store"]')
  if (!storeDropdown) {
    showStoreDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
