<template>
  <!-- Modal Overlay -->
  <div
    v-if="isVisible && notification"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]"
    @click.self="closeModal"
  >
    <!-- Modal Content -->
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-lg mx-4 max-h-[90vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-white px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-teal-950 font-roboto">Notifications</h2>
          <button
            @click="closeModal"
            class="text-cyan-600 hover:text-cyan-700 transition-colors p-1 rounded-full hover:bg-gray-100"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div class="text-gray-600 text-sm font-roboto mt-1">
          You have {{ unreadCount }} messages
        </div>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
        <!-- Notification Content -->
        <div class="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6">
          <!-- Notification Icon and Title -->
          <div class="flex items-start gap-3 mb-4">
            <div 
              :class="[
                'w-6 h-6 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0',
                getNotificationIconClass(notification.type)
              ]"
            >
              {{ getNotificationIcon(notification.type) }}
            </div>
            <h3 class="text-teal-950 text-lg font-bold font-roboto flex-1">{{ notification.title }}</h3>
          </div>

          <!-- COMMON_NOTIFICATION Type -->
          <div v-if="notification.type === 'COMMON_NOTIFICATION'" class="space-y-3">
            <div v-if="notification.details && notification.details.length > 0">
              <ul class="space-y-2">
                <li
                  v-for="(detail, index) in notification.details"
                  :key="index"
                  class="flex items-center gap-2 text-gray-700 text-sm font-roboto"
                >
                  <span class="w-1.5 h-1.5 bg-gray-400 rounded-full flex-shrink-0"></span>
                  {{ detail }}
                </li>
              </ul>
            </div>
            <div v-else class="text-gray-700 text-sm font-roboto">
              {{ notification.content }}
            </div>
          </div>

          <!-- ACTIVITY_NOTIFICATION Type -->
          <div v-else-if="notification.type === 'ACTIVITY_NOTIFICATION'" class="space-y-4">
            <p class="text-gray-700 text-sm font-roboto">
              Current client, <span class="font-bold text-teal-950">{{ notification.clientName }}</span>, is not assigned yet. Would you like to assign this client for your login?
            </p>
          </div>

          <!-- WARNING_NOTIFICATION Type -->
          <div v-else-if="notification.type === 'WARNING_NOTIFICATION'" class="space-y-4">
            <p class="text-gray-700 text-sm font-roboto mb-3">
              {{ notification.content }}
            </p>
            
            <div v-if="notification.loanNumbers && notification.details" class="space-y-2">
              <div 
                v-for="(loanNumber, index) in notification.loanNumbers" 
                :key="index"
                class="flex items-center justify-between py-2"
              >
                <span class="text-cyan-600 text-sm font-medium font-roboto cursor-pointer hover:underline">
                  {{ loanNumber }}
                </span>
                <span class="text-gray-600 text-sm font-roboto">
                  {{ notification.details[index] }}
                </span>
              </div>
              
              <div class="mt-4 flex items-center gap-2">
                <input 
                  type="checkbox" 
                  id="ndr-acknowledged" 
                  class="w-4 h-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500"
                />
                <label for="ndr-acknowledged" class="text-gray-700 text-sm font-roboto">
                  NDR Acknowledged
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <!-- Navigation -->
          <div class="flex items-center gap-4">
            <button
              @click="previousNotification"
              :disabled="currentIndex <= 0"
              class="text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <span class="text-sm text-gray-600 font-roboto">
              {{ currentIndex + 1 }} / {{ totalCount }}
            </span>

            <button
              @click="nextNotification"
              :disabled="currentIndex >= totalCount - 1"
              class="text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3">
            <!-- Assignment specific buttons -->
            <div v-if="notification.type === 'ACTIVITY_NOTIFICATION'" class="flex gap-3">
              <button
                @click="handleAssignmentAction(false)"
                class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium font-roboto"
              >
                No
              </button>
              <button
                @click="handleAssignmentAction(true)"
                class="px-4 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors font-medium font-roboto"
              >
                Yes
              </button>
            </div>
            
            <!-- General OK button for other types -->
            <button
              v-else
              @click="handleOkAction"
              class="px-4 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors font-medium font-roboto"
            >
              Ok
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// No Vue imports needed since we only use props

interface Message {
  id: string
  orderNumber: string
  title: string
  content: string
  createdAt: Date
  priority: 'high' | 'medium' | 'low'
  status: 'unread' | 'read'
  sender: string
  category: string
  type: 'COMMON_NOTIFICATION' | 'ACTIVITY_NOTIFICATION' | 'WARNING_NOTIFICATION'
  details?: string[]
  clientName?: string
  loanNumbers?: string[]
  actionRequired?: boolean
}

interface Props {
  isVisible: boolean
  notification: Message | null
  currentIndex: number
  totalCount: number
  unreadCount: number
}

interface Emits {
  (e: 'close'): void
  (e: 'next', notification: Message): void
  (e: 'previous', notification: Message): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// No longer need local mock data since we get everything from parent props

// No longer need local currentIndex since it comes from props

// Methods
const closeModal = () => {
  emit('close')
}



const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'COMMON_NOTIFICATION': return 'i'
    case 'WARNING_NOTIFICATION': return '!'
    case 'ACTIVITY_NOTIFICATION': return '?'
    default: return 'i'
  }
}

const getNotificationIconClass = (type: string) => {
  switch (type) {
    case 'COMMON_NOTIFICATION': return 'bg-blue-500'
    case 'WARNING_NOTIFICATION': return 'bg-orange-500'
    case 'ACTIVITY_NOTIFICATION': return 'bg-green-500'
    default: return 'bg-gray-500'
  }
}

const previousNotification = () => {
  if (props.currentIndex > 0 && props.notification) {
    emit('previous', props.notification)
  }
}

const nextNotification = () => {
  if (props.currentIndex < props.totalCount - 1 && props.notification) {
    emit('next', props.notification)
  }
}

const handleAssignmentAction = (assign: boolean) => {
  console.log(`Assignment action: ${assign ? 'Yes' : 'No'}`)
  // Handle assignment logic here
  closeModal()
}

const handleOkAction = () => {
  console.log('OK action')
  closeModal()
}
</script>

<style scoped>
.font-roboto {
  font-family: 'Roboto', sans-serif;
}

/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
